from django.db import models

class RiskGroup(models.Model):
    """Replacement for the original RiskGroup model"""
    uid = models.CharField(max_length=255, unique=True)
    group_name = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        db_table = 'riskradar_riskgroup'  # Use the same table name as the original
        app_label = 'api_ninja'  # Explicitly set the app_label
