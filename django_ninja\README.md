# ESGIS Django Ninja Backend

A modern Django Ninja implementation of the ESGIS backend API, providing a complete replacement for the existing Django REST Framework backend.

## Features

- **Modern API Framework**: Built with Django Ninja for automatic OpenAPI documentation and high performance
- **Complete CRUD Operations**: Full support for Address, Sale, Valuation, and Customer management
- **GIS Support**: PostGIS integration for geospatial data handling
- **Authentication**: LDAP integration with fallback to Django authentication
- **Modular Architecture**: Separate apps for different business domains (Green, Finance, CCRA, PropertyFlow, RiskRadar)
- **Frontend Compatible**: Designed to work seamlessly with the existing React frontend

## Project Structure

```
django_ninja/
├── esgis_ninja/                 # Main Django project
│   ├── settings/               # Environment-specific settings
│   ├── urls.py                 # Main URL configuration
│   ├── wsgi.py                 # WSGI application
│   └── asgi.py                 # ASGI application
├── api_ninja/                  # Core API app
│   ├── schemas.py              # Pydantic schemas
│   ├── api.py                  # Main API router
│   └── routers/                # Individual API routers
│       ├── address.py          # Address endpoints
│       ├── sale.py             # Sale endpoints
│       ├── valuation.py        # Valuation endpoints
│       └── customer.py         # Customer endpoints
├── green_ninja/                # Green/sustainability module
├── finance_ninja/              # Finance module
├── ccra_ninja/                 # CCRA module
├── propertyflow_ninja/         # PropertyFlow module
├── riskradar_ninja/            # RiskRadar module
├── manage.py                   # Django management script
├── pyproject.toml              # Project dependencies and configuration
└── README.md                   # This file
```

## Quick Start

### Prerequisites

- Python 3.8+
- PostgreSQL with PostGIS extension
- Redis (for caching and background tasks)

### Installation

1. **Clone and navigate to the project:**
   ```bash
   cd django_ninja
   ```

2. **Install dependencies:**
   ```bash
   pip install -e .
   ```

3. **Set up environment variables:**
   ```bash
   export DJANGO_SETTINGS_MODULE=esgis_ninja.settings.development
   export DB_NAME=agrigis_dev
   export DB_USERNAME=postgres
   export DB_PASSWORD=postgres
   export DB_HOST=localhost
   export DB_PORT=5432
   ```

4. **Run database migrations:**
   ```bash
   python manage.py migrate
   ```

5. **Create a superuser:**
   ```bash
   python manage.py createsuperuser
   ```

6. **Start the development server:**
   ```bash
   python manage.py runserver
   ```

### API Documentation

Once the server is running, you can access:

- **API Documentation**: http://localhost:8000/api/docs/
- **Health Check**: http://localhost:8000/api/health
- **Admin Interface**: http://localhost:8000/admin/

## API Endpoints

### Core Endpoints

- `GET /api/health` - Health check
- `GET /api/auth/user` - Get current user info
- `GET /api/auth/permissions` - Get user permissions

### Address Endpoints

- `GET /api/addresses/` - List addresses with pagination and filtering
- `GET /api/addresses/{id}` - Get specific address
- `POST /api/addresses/` - Create new address
- `PUT /api/addresses/{id}` - Update address
- `DELETE /api/addresses/{id}` - Delete address
- `GET /api/addresses/search/?q={query}` - Search addresses

### Sale Endpoints

- `GET /api/sales/` - List sales with filtering
- `GET /api/sales/{id}` - Get specific sale
- `POST /api/sales/` - Create new sale
- `PUT /api/sales/{id}` - Update sale
- `DELETE /api/sales/{id}` - Delete sale
- `GET /api/sales/by-address/{address_id}` - Get sales for address

### Valuation Endpoints

- `GET /api/valuations/` - List valuations with filtering
- `GET /api/valuations/{id}` - Get specific valuation
- `POST /api/valuations/` - Create new valuation
- `PUT /api/valuations/{id}` - Update valuation
- `DELETE /api/valuations/{id}` - Delete valuation
- `GET /api/valuations/by-address/{address_id}` - Get valuations for address

### Customer Endpoints

- `GET /api/customers/` - List customers with filtering
- `GET /api/customers/{id}` - Get specific customer
- `POST /api/customers/` - Create new customer
- `GET /api/customers/search/?q={query}` - Search customers
- `GET /api/customers/by-segment/{segment}` - Get customers by segment

### Module-Specific Endpoints

- `GET /api/green/health` - Green module health check
- `GET /api/finance/health` - Finance module health check
- `GET /api/ccra/health` - CCRA module health check
- `GET /api/propertyflow/health` - PropertyFlow module health check
- `GET /api/riskradar/health` - RiskRadar module health check

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DJANGO_SETTINGS_MODULE` | Django settings module | `esgis_ninja.settings.development` |
| `SECRET_KEY` | Django secret key | Auto-generated |
| `DEBUG` | Enable debug mode | `False` |
| `DB_NAME` | Database name | `agrigis` |
| `DB_USERNAME` | Database username | `postgres` |
| `DB_PASSWORD` | Database password | Required |
| `DB_HOST` | Database host | `localhost` |
| `DB_PORT` | Database port | `5432` |
| `REDIS_HOST` | Redis host | `localhost` |
| `REDIS_PORT` | Redis port | `6379` |
| `LDAP_BIND_DN` | LDAP bind DN | Required for production |
| `LDAP_BIND_PASSWORD` | LDAP bind password | Required for production |

### Settings Modules

- **Development**: `esgis_ninja.settings.development`
  - Debug enabled
  - Local database
  - Console email backend
  - Debug toolbar enabled

- **Production**: `esgis_ninja.settings.production`
  - Debug disabled
  - LDAP authentication
  - Security headers enabled
  - Redis caching

## Development

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black .
isort .
```

### Linting

```bash
flake8
```

## Deployment

### Production Setup

1. Set environment variables for production
2. Use `esgis_ninja.settings.production` settings module
3. Configure LDAP authentication
4. Set up Redis for caching
5. Use a WSGI server like Gunicorn

### Docker Deployment

```bash
# Build image
docker build -t esgis-ninja .

# Run container
docker run -p 8000:8000 -e DJANGO_SETTINGS_MODULE=esgis_ninja.settings.production esgis-ninja
```

## Migration from Django REST Framework

This Django Ninja implementation is designed to be a drop-in replacement for the existing Django REST Framework backend. The API endpoints maintain compatibility with the existing frontend while providing improved performance and automatic documentation.

### Key Differences

- **Automatic Documentation**: OpenAPI/Swagger docs generated automatically
- **Type Safety**: Pydantic schemas provide runtime type validation
- **Performance**: Faster request/response handling
- **Modern Syntax**: Cleaner, more maintainable code

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
