"""
Development settings for ESGIS Ninja project.
"""

import os
from .base import *

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

# Development hosts
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# CORS settings for development
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]

# Database for development (can use SQLite for quick setup)
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': os.environ.get('DB_NAME', 'agrigis_dev'),
        'USER': os.environ.get('DB_USERNAME', 'postgres'),
        'PASSWORD': os.environ.get('DB_PASSWORD', 'postgres'),
        'HOST': os.environ.get('POSTGRESQL_SERVICE_HOST', 'localhost'),
        'PORT': os.environ.get('POSTGRESQL_SERVICE_PORT', '5432'),
        'CONN_MAX_AGE': 0,  # Disable connection pooling in development
    }
}

# Add debug tools for development (optional)
# try:
#     import debug_toolbar
#     INSTALLED_APPS = INSTALLED_APPS + ['debug_toolbar']
#     MIDDLEWARE = MIDDLEWARE + ['debug_toolbar.middleware.DebugToolbarMiddleware']
#
#     # Debug toolbar configuration
#     INTERNAL_IPS = [
#         '127.0.0.1',
#         'localhost',
#     ]
# except ImportError:
#     print("Debug toolbar not available - install with: pip install django-debug-toolbar")
#
# try:
#     import silk
#     INSTALLED_APPS = INSTALLED_APPS + ['django_silk']
#     MIDDLEWARE = MIDDLEWARE + ['silk.middleware.SilkyMiddleware']
# except ImportError:
#     print("Django Silk not available - install with: pip install django-silk")

# Disable LDAP authentication in development
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
]

# Development logging - commented out for now due to import issues
# LOGGING['root']['level'] = 'DEBUG'
# LOGGING['loggers']['django']['level'] = 'DEBUG'

# Email backend for development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Cache configuration for development
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Static files configuration for development
STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'

# Development-specific settings
ANONYMIZE_VALUES = False

# Disable HTTPS redirects in development
SECURE_SSL_REDIRECT = False
SECURE_PROXY_SSL_HEADER = None

# Session and CSRF configuration for development and frontend compatibility
SESSION_COOKIE_SECURE = False
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'

CSRF_COOKIE_SECURE = False
CSRF_COOKIE_HTTPONLY = False  # Allow JavaScript to read CSRF token
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_HEADER_NAME = 'HTTP_X_CSRFTOKEN'
CSRF_COOKIE_NAME = 'csrftoken'

# CSRF trusted origins for frontend
CSRF_TRUSTED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]

# Development Redis configuration
RQ_QUEUES = {
    'default': {
        'HOST': 'localhost',
        'PORT': 6379,
        'DB': 0,
        'DEFAULT_TIMEOUT': 360,
    }
}
