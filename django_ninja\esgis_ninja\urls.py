from django.contrib import admin
from django.urls import path, include, re_path
from django.http import HttpResponse
from django.shortcuts import render
from ninja import NinjaAPI
import ninja.errors
from django.conf import settings
import os

# Create main API instance
api = NinjaAPI(
    title="ESGIS API",
    version="1.0.0",
    description="ESGIS Django Ninja API",
    docs_url="/api/docs/",
)

# Global flag to track if routers have been added
_ROUTERS_ADDED = False

def add_routers_once():
    """Add routers only once to avoid duplicate registration"""
    global _ROUTERS_ADDED
    if _ROUTERS_ADDED:
        return
    
    try:
        # Import and add API routers
        from api_ninja.api import api as api_ninja_router
        from green_ninja.api import api as green_ninja_router
        from finance_ninja.api import api as finance_ninja_router
        from ccra_ninja.api import api as ccra_ninja_router
        from propertyflow_ninja.api import api as propertyflow_ninja_router
        from riskradar_ninja.api import api as riskradar_ninja_router
        
        # Add routers to main API
        api.add_router("", api_ninja_router)
        api.add_router("/green", green_ninja_router)
        api.add_router("/finance", finance_ninja_router)
        api.add_router("/ccra", ccra_ninja_router)
        api.add_router("/propertyflow", propertyflow_ninja_router)
        api.add_router("/riskradar", riskradar_ninja_router)
        
        _ROUTERS_ADDED = True
    except ImportError as e:
        # Handle case where apps are not yet ready
        print(f"Warning: Could not import API routers: {e}")

def static_serve(request, path):
    """Serve static files for development"""
    from django.http import FileResponse, Http404
    import os
    
    static_path = os.path.join(settings.BASE_DIR, 'static', path)
    if os.path.exists(static_path):
        return FileResponse(open(static_path, 'rb'))
    raise Http404("File not found")

def index_view(request):
    """Serve the frontend index.html for SPA routing"""
    try:
        # Try to serve the built frontend
        index_path = os.path.join(settings.BASE_DIR, 'static', 'index.html')
        if os.path.exists(index_path):
            with open(index_path, 'r') as f:
                return HttpResponse(f.read(), content_type='text/html')
    except:
        pass
    
    # Fallback to a simple response
    return HttpResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>ESGIS Django Ninja Backend</title>
    </head>
    <body>
        <h1>ESGIS Django Ninja Backend</h1>
        <p>API Documentation: <a href="/api/docs/">/api/docs/</a></p>
        <p>Health Check: <a href="/api/health">/api/health</a></p>
    </body>
    </html>
    """, content_type='text/html')

# Call add_routers_once before defining urlpatterns
add_routers_once()

urlpatterns = [
    path("/", index_view, name="index"),  # Root URL serves the frontend
    path("admin/", admin.site.urls),
    path("advanced_filters/", include("advanced_filters.urls")),
    path("api/", api.urls),  # Changed from "" to "api/" to avoid conflicts
    path("favicon.ico", static_serve, kwargs={"path": "favicon.ico"}),
    path("agrigis.svg", static_serve, kwargs={"path": "agrigis.svg"}),
    path("openshift.svg", static_serve, kwargs={"path": "openshift.svg"}),
    # Catch-all for frontend routes (SPA routing)
    re_path(r"^.*$", index_view),
]
