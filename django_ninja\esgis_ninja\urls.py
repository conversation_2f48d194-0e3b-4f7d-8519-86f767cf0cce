from django.contrib import admin
from django.urls import path, include, re_path
from django.http import HttpResponse
from django.shortcuts import render
from ninja import NinjaAPI
import ninja.errors
from django.conf import settings
import os

# Create main API instance
api = NinjaAPI(
    title="ESGIS API",
    version="1.0.0",
    description="ESGIS Django Ninja API",
    docs_url="/api/docs/",
)

# Import and add API routers - do this only once, in the main urls.py
try:
    # Import API routers
    from api_ninja.api import api as api_ninja_router
    from green_ninja.api import api as green_ninja_router
    from finance_ninja.api import api as finance_ninja_router
    from ccra_ninja.api import api as ccra_ninja_router
    from propertyflow_ninja.api import api as propertyflow_ninja_router
    from riskradar_ninja.api import api as riskradar_ninja_router

    # Add routers to main API
    api.add_router("", api_ninja_router)
    api.add_router("/green", green_ninja_router)
    api.add_router("/finance", finance_ninja_router)
    api.add_router("/ccra", ccra_ninja_router)
    api.add_router("/propertyflow", propertyflow_ninja_router)
    api.add_router("/riskradar", riskradar_ninja_router)
except ImportError as e:
    # Handle case where apps are not yet ready
    print(f"Warning: Could not import API routers: {e}")

def static_serve(request, path):
    """Serve static files for development"""
    from django.http import FileResponse, Http404
    import os

    static_path = os.path.join(settings.BASE_DIR, 'static', path)
    if os.path.exists(static_path):
        return FileResponse(open(static_path, 'rb'))
    raise Http404("File not found")

def index_view(request):
    """Serve the frontend index.html"""
    return static_serve(request, path="index.html")

urlpatterns = [
    path("/", index_view, name="index"),  # Root URL serves the frontend
    path("admin/", admin.site.urls),
    path("advanced_filters/", include("advanced_filters.urls")),
    path("api/", api.urls),  # Changed from "" to "api/" to avoid conflicts
    path("favicon.ico", static_serve, kwargs={"path": "favicon.ico"}),
    path("agrigis.svg", static_serve, kwargs={"path": "agrigis.svg"}),
    path("openshift.svg", static_serve, kwargs={"path": "openshift.svg"}),
    # Catch-all for frontend routes (SPA routing)
    re_path(r"^.*$", index_view),
]
