"""
Django settings for ESGIS Ninja project.

Generated by 'django-admin startproject' using Django 4.2.17.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
import structlog
from pathlib import Path

# GeoDjango settings
# Default SRID for storing geometries in the database
DEFAULT_GLOBAL_SRID = 3857

# Default SRID for frontend display (WGS84 - latitude/longitude)
DEFAULT_FRONTEND_SRID = 4326

# Default geography-specific SRIDs
DEFAULT_GEOGRAPHY_SRID_LOOKUP = {
    "AU": 7855,
    "NZ": 2193,
}

# Geography-specific SRID overrides
DEFAULT_SRID_OVERRIDE = {
    "AU": {
        "Victoria": 7856,
        "Western Australia": 7850,
        "Tasmania": 7856,
        "Australian Capital Territory": 7858,
        "South Australia": 7855,
        "Other Territories": 7859,
        "Queensland": 7857,
        "New South Wales": 7856,
        "Northern Territory": 7854,
    }
}

# Default geography
GEOGRAPHY = os.environ.get("GEOGRAPHY", "NZ")

# Default SRID for the current geography
DEFAULT_SRID = DEFAULT_GEOGRAPHY_SRID_LOOKUP.get(
    GEOGRAPHY
) or DEFAULT_GEOGRAPHY_SRID_LOOKUP.get("AU")

# Default map locations
DEFAULT_MAP_LOCATION_LOOKUP = {
    "AU": "srid=4326;POINT (144.944694882992 -37.8216585755085)",
    "NZ": "srid=4326;POINT (174.84 -41.29)",
}

DEFAULT_MAP_LOCATION_EWKT = os.environ.get(
    "DEFAULT_MAP_LOCATION_EWKT", DEFAULT_MAP_LOCATION_LOOKUP.get(GEOGRAPHY)
)

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY', 'django-insecure-change-me-in-production')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true'

ALLOWED_HOSTS = ['*']

# Application definition
INSTALLED_APPS = [
    'whitenoise.runserver_nostatic',
    'django.contrib.contenttypes',
    'advanced_filters',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.humanize',
    'django.contrib.gis',
    'corsheaders',
    'safedelete',
    'simple_history',
    'ninja',
    'django.contrib.staticfiles',
    'django_extensions',
    'django_filters',
    'django_rq',
    'axes',

    # Local apps
    'esgis_ninja',
    'api_ninja',  # Make sure this is before api
    'riskradar_ninja',
    'green_ninja',
    'finance_ninja',
    'ccra_ninja',
    'propertyflow_ninja',

    # Original apps with models
    'api',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'axes.middleware.AxesMiddleware',
]

ROOT_URLCONF = 'esgis_ninja.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'esgis_ninja.wsgi.application'

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': os.environ.get('DB_NAME', 'agrigis'),
        'USER': os.environ.get('DB_USERNAME', 'django'),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('POSTGRESQL_SERVICE_HOST', 'localhost'),
        'PORT': os.environ.get('POSTGRESQL_SERVICE_PORT', '5432'),
        'CONN_MAX_AGE': 240,
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'Pacific/Auckland'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")

# Add the frontend build directory to STATICFILES_DIRS
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
    # Add the path to your frontend build directory
    os.path.join(BASE_DIR, "..", "client", "build"),  # Adjust this path as needed
]

# Ensure WhiteNoise is configured correctly
STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# CORS settings
CORS_ORIGIN_ALLOW_ALL = DEBUG
CORS_ALLOW_CREDENTIALS = True

if not DEBUG:
    CORS_ORIGIN_WHITELIST = [
        'https://customspace.global.anz.com',
    ]

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
}

# Structlog configuration
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

# Redis configuration
REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')

RQ_QUEUES = {
    'default': {
        'HOST': os.environ.get('REDIS_HOST', 'localhost'),
        'PORT': int(os.environ.get('REDIS_PORT', 6379)),
        'DB': 0,
        'PASSWORD': os.environ.get('REDIS_PASSWORD', ''),
        'DEFAULT_TIMEOUT': 360,
    },
}

# Teradata configuration
TERADATA_CONNECTION_STRING = os.environ.get('TERADATA_CONNECTION_STRING', '')

# S3 Configuration
AWS_ACCESS_KEY_ID = os.environ.get('S3_IIH_INBOUND_ACCESS_KEY_ID', '')
AWS_SECRET_ACCESS_KEY = os.environ.get('S3_IIH_INBOUND_SECRET_KEY', '')
AWS_STORAGE_BUCKET_NAME = os.environ.get('S3_IIH_INBOUND_BUCKET_NAME', '')
AWS_S3_ENDPOINT_URL = os.environ.get('S3_IIH_INBOUND_HOST', '')

# Base URL for the application
BASE_URL = os.environ.get('BASE_URL', 'http://localhost:8000')

# Anonymization settings
ANONYMIZE_VALUES = os.environ.get('ANONYMIZE_VALUES', 'False').lower() == 'true'

# Date formats
LONG_DATE_FORMAT = '%d %B %Y'
SHORT_DATE_FORMAT = '%d/%m/%Y'
