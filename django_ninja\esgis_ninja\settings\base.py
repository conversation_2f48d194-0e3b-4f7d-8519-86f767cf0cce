"""
Django settings for ESGIS Ninja project.

Generated by 'django-admin startproject' using Django 4.2.17.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
import structlog
from pathlib import Path
from datetime import timedelta

# GeoDjango settings
# Default SRID for storing geometries in the database
DEFAULT_GLOBAL_SRID = 3857

# Default SRID for frontend display (WGS84 - latitude/longitude)
DEFAULT_FRONTEND_SRID = 4326

# Default geography-specific SRIDs
DEFAULT_GEOGRAPHY_SRID_LOOKUP = {
    "AU": 7855,
    "NZ": 2193,
}

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY', 'django-insecure-change-me-in-production')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'

ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', 'localhost,127.0.0.1').split(',')

# Application definition
INSTALLED_APPS = [
    'whitenoise.runserver_nostatic',
    'django.contrib.contenttypes',
    'advanced_filters',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.humanize',
    'django.contrib.gis',
    'corsheaders',
    'safedelete',
    'simple_history',
    'ninja',
    'django.contrib.staticfiles',
    'django_extensions',
    'django_filters',
    'django_rq',
    'axes',

    # Local apps
    'esgis_ninja',
    'api_ninja',  # Make sure this is before api
    'riskradar_ninja',
    'green_ninja',
    'finance_ninja',
    'ccra_ninja',
    'propertyflow_ninja',

    # Original apps with models
    'api',
    'main',
    'green',
    'riskradar',
    'linz_importer',
    'propertyflow',
    'ccra',
    'finance',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'axes.middleware.AxesMiddleware',
]

ROOT_URLCONF = 'esgis_ninja.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'esgis_ninja.wsgi.application'

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': os.environ.get('DB_NAME', 'agrigis'),
        'USER': os.environ.get('DB_USERNAME', 'django'),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('POSTGRESQL_SERVICE_HOST', 'localhost'),
        'PORT': os.environ.get('POSTGRESQL_SERVICE_PORT', '5432'),
        'CONN_MAX_AGE': 240,
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'Pacific/Auckland'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# CORS settings
CORS_ALLOW_ALL_ORIGINS = DEBUG
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

# Session configuration
SESSION_COOKIE_AGE = 86400  # 24 hours
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = False

# Cache configuration
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Security settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# Axes configuration for login protection
AXES_FAILURE_LIMIT = 5
AXES_COOLOFF_TIME = timedelta(hours=1)
AXES_LOCKOUT_PARAMETERS = ["username"]
AXES_COOLOFF_MESSAGE = "Your account has been temporarily locked due to multiple unsuccessful login attempts. Please try again in 1 hour or contact support."

# Redis configuration
RQ_QUEUES = {
    'default': {
        'HOST': os.environ.get('REDIS_HOST', 'localhost'),
        'PORT': os.environ.get('REDIS_PORT', 6379),
        'DB': 0,
        'PASSWORD': os.environ.get('REDIS_PASSWORD', ''),
        'DEFAULT_TIMEOUT': 360,
    }
}

# Teradata configuration
TERADATA_CONNECTION_STRING = os.environ.get('TERADATA_CONNECTION_STRING', '')

# S3 Configuration
AWS_ACCESS_KEY_ID = os.environ.get('S3_IIH_INBOUND_ACCESS_KEY_ID', '')
AWS_SECRET_ACCESS_KEY = os.environ.get('S3_IIH_INBOUND_SECRET_KEY', '')
AWS_STORAGE_BUCKET_NAME = os.environ.get('S3_IIH_INBOUND_BUCKET_NAME', '')
AWS_S3_ENDPOINT_URL = os.environ.get('S3_IIH_INBOUND_HOST', '')

# Base URL for the application
BASE_URL = os.environ.get('BASE_URL', 'http://localhost:8000')

# Anonymization settings
ANONYMIZE_VALUES = os.environ.get('ANONYMIZE_VALUES', 'False').lower() == 'true'

# Date formats
LONG_DATE_FORMAT = '%d %B %Y'
SHORT_DATE_FORMAT = '%d/%m/%Y'
