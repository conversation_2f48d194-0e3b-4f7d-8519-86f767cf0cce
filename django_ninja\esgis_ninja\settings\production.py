"""
Production settings for ESGIS Ninja project.
"""

from .base import *
import os

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

# Production hosts - should be set via environment variable
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',')

# Security settings for production
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# Session and CSRF cookies
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# CORS settings for production
CORS_ORIGIN_ALLOW_ALL = False
CORS_ORIGIN_WHITELIST = [
    'https://customspace.global.anz.com/',
]

# LDAP Authentication configuration
LDAP_BIND_DN = os.environ.get('LDAP_BIND_DN')
LDAP_BIND_PASSWORD = os.environ.get('LDAP_BIND_PASSWORD')
LDAP_SERVER_URI = os.environ.get('LDAP_SERVER_URI', 'ldaps://global-ldap.lb.apps.anz:636')

if not (LDAP_BIND_DN and LDAP_BIND_PASSWORD):
    raise Exception(
        "ESGIS requires LDAP for authenticating users but LDAP_BIND_DN / LDAP_BIND_PASSWORD are not set."
    )

AUTHENTICATION_BACKENDS = [
    "axes.backends.AxesStandaloneBackend",
    "django_auth_ldap.backend.LDAPBackend",
    "django.contrib.auth.backends.ModelBackend",
]

# LDAP Configuration
import ldap
from django_auth_ldap.config import LDAPSearch, GroupOfNamesType

AUTH_LDAP_SERVER_URI = LDAP_SERVER_URI
AUTH_LDAP_CONNECTION_OPTIONS = {
    ldap.OPT_X_TLS_CACERTFILE: "/etc/pki/tls/certs/ca-bundle.crt",
    ldap.OPT_X_TLS_NEWCTX: 0,
}

AUTH_LDAP_BIND_DN = LDAP_BIND_DN
AUTH_LDAP_BIND_PASSWORD = LDAP_BIND_PASSWORD

AUTH_LDAP_USER_SEARCH = LDAPSearch(
    "ou=people,dc=anz,dc=com",
    ldap.SCOPE_SUBTREE,
    "(uid=%(user)s)"
)

AUTH_LDAP_USER_ATTR_MAP = {
    "first_name": "givenName",
    "last_name": "sn",
    "email": "mail"
}

# Production database configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': os.environ.get('DB_NAME', 'agrigis'),
        'USER': os.environ.get('DB_USERNAME'),
        'PASSWORD': os.environ.get('DB_PASSWORD'),
        'HOST': os.environ.get('POSTGRESQL_SERVICE_HOST'),
        'PORT': os.environ.get('POSTGRESQL_SERVICE_PORT', '5432'),
        'CONN_MAX_AGE': 240,
        'OPTIONS': {
            'sslmode': 'require',
        },
    }
}

# Cache configuration for production
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://localhost:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# Enable anonymization in production
ANONYMIZE_VALUES = True

# Base URL for production
BASE_URL = os.environ.get(
    "BASE_URL", "https://agrigis-ca-insights-prod.caas.nz.service.anz/"
)

# Static files configuration for production
STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'

# Email configuration for production
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.anz.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')

# Production logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'WARNING',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'esgis_ninja': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Production Redis configuration
RQ_QUEUES = {
    'default': {
        'HOST': os.environ.get('REDIS_HOST', 'localhost'),
        'PORT': int(os.environ.get('REDIS_PORT', '6379')),
        'DB': 0,
        'PASSWORD': os.environ.get('REDIS_PASSWORD', ''),
        'DEFAULT_TIMEOUT': 360,
    }
}
