<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESGIS Django Ninja Backend</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }
        .api-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .api-link {
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }
        .api-link:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }
        .api-link h3 {
            margin: 0 0 0.5rem 0;
            color: #667eea;
        }
        .api-link p {
            margin: 0;
            font-size: 0.9rem;
            color: #666;
        }
        .status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            background: #28a745;
            color: white;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>ESGIS Django Ninja Backend</h1>
        <p>Modern API backend built with Django Ninja</p>
        <span class="status">✓ Running</span>
    </div>

    <div class="api-links">
        <a href="/api/docs/" class="api-link">
            <h3>📚 API Documentation</h3>
            <p>Interactive OpenAPI/Swagger documentation for all endpoints</p>
        </a>

        <a href="/api/health" class="api-link">
            <h3>💚 Health Check</h3>
            <p>Check the API health status</p>
        </a>

        <a href="/api/addresses/" class="api-link">
            <h3>🏠 Addresses API</h3>
            <p>Manage property addresses with GIS support</p>
        </a>

        <a href="/api/sales/" class="api-link">
            <h3>💰 Sales API</h3>
            <p>Property sales data and transactions</p>
        </a>

        <a href="/api/valuations/" class="api-link">
            <h3>📊 Valuations API</h3>
            <p>Property valuations and assessments</p>
        </a>

        <a href="/api/customers/" class="api-link">
            <h3>👥 Customers API</h3>
            <p>Customer management and data</p>
        </a>

        <a href="/admin/" class="api-link">
            <h3>⚙️ Admin Interface</h3>
            <p>Django admin panel for data management</p>
        </a>

        <a href="/api/auth/user" class="api-link">
            <h3>🔐 Authentication</h3>
            <p>User authentication and permissions</p>
        </a>
    </div>

    <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
        <h3>Module Health Checks</h3>
        <ul>
            <li><a href="/api/green/health">Green Module</a> - Environmental data</li>
            <li><a href="/api/finance/health">Finance Module</a> - Financial calculations</li>
            <li><a href="/api/ccra/health">CCRA Module</a> - Credit risk assessment</li>
            <li><a href="/api/propertyflow/health">PropertyFlow Module</a> - Property workflows</li>
            <li><a href="/api/riskradar/health">RiskRadar Module</a> - Risk monitoring</li>
        </ul>
    </div>

    <footer style="margin-top: 2rem; text-align: center; color: #666; font-size: 0.9rem;">
        <p>ESGIS Django Ninja Backend v1.0.0</p>
        <p>Built with Django Ninja for high performance and automatic documentation</p>
    </footer>
</body>
</html>
