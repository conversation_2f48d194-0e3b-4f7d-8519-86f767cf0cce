from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
import os


class Command(BaseCommand):
    help = 'Set up Django Ninja backend with initial data and permissions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-superuser',
            action='store_true',
            help='Create a superuser account',
        )
        parser.add_argument(
            '--username',
            type=str,
            help='Username for superuser (if creating)',
            default='admin'
        )
        parser.add_argument(
            '--email',
            type=str,
            help='Email for superuser (if creating)',
            default='<EMAIL>'
        )
        parser.add_argument(
            '--password',
            type=str,
            help='Password for superuser (if creating)',
            default='admin123'
        )
        parser.add_argument(
            '--setup-groups',
            action='store_true',
            help='Set up default user groups and permissions',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Setting up Django Ninja backend...')
        )

        try:
            with transaction.atomic():
                if options['setup_groups']:
                    self.setup_groups_and_permissions()

                if options['create_superuser']:
                    self.create_superuser(
                        options['username'],
                        options['email'],
                        options['password']
                    )

                self.check_database_connection()
                self.check_api_endpoints()

            self.stdout.write(
                self.style.SUCCESS('Django Ninja backend setup completed successfully!')
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Setup failed: {str(e)}')
            )
            raise CommandError(f'Setup failed: {str(e)}')

    def setup_groups_and_permissions(self):
        """Set up default user groups and permissions"""
        self.stdout.write('Setting up user groups and permissions...')

        # Create groups
        groups_data = [
            {
                'name': 'API Users',
                'permissions': [
                    'view_address',
                    'view_sale',
                    'view_valuation',
                    'view_customer',
                ]
            },
            {
                'name': 'API Editors',
                'permissions': [
                    'view_address', 'add_address', 'change_address',
                    'view_sale', 'add_sale', 'change_sale',
                    'view_valuation', 'add_valuation', 'change_valuation',
                    'view_customer', 'add_customer', 'change_customer',
                ]
            },
            {
                'name': 'API Administrators',
                'permissions': [
                    'view_address', 'add_address', 'change_address', 'delete_address',
                    'view_sale', 'add_sale', 'change_sale', 'delete_sale',
                    'view_valuation', 'add_valuation', 'change_valuation', 'delete_valuation',
                    'view_customer', 'add_customer', 'change_customer', 'delete_customer',
                ]
            }
        ]

        for group_data in groups_data:
            group, created = Group.objects.get_or_create(name=group_data['name'])
            if created:
                self.stdout.write(f'Created group: {group.name}')
            else:
                self.stdout.write(f'Group already exists: {group.name}')

            # Add permissions to group (if they exist)
            for perm_codename in group_data['permissions']:
                try:
                    permission = Permission.objects.get(codename=perm_codename)
                    group.permissions.add(permission)
                except Permission.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'Permission not found: {perm_codename}')
                    )

    def create_superuser(self, username, email, password):
        """Create a superuser account"""
        self.stdout.write(f'Creating superuser: {username}...')

        if User.objects.filter(username=username).exists():
            self.stdout.write(
                self.style.WARNING(f'Superuser {username} already exists')
            )
            return

        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password
        )

        self.stdout.write(
            self.style.SUCCESS(f'Superuser {username} created successfully')
        )

    def check_database_connection(self):
        """Check database connection"""
        self.stdout.write('Checking database connection...')

        from django.db import connection
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result:
                    self.stdout.write(
                        self.style.SUCCESS('Database connection: OK')
                    )
        except Exception as e:
            raise CommandError(f'Database connection failed: {str(e)}')

    def check_api_endpoints(self):
        """Check that API endpoints are accessible"""
        self.stdout.write('Checking API endpoints...')

        try:
            from django.test import Client
            client = Client()

            # Test health endpoint
            response = client.get('/api/health')
            if response.status_code == 200:
                self.stdout.write(
                    self.style.SUCCESS('Health endpoint: OK')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Health endpoint returned: {response.status_code}')
                )

            # Test API documentation
            response = client.get('/api/docs/')
            if response.status_code == 200:
                self.stdout.write(
                    self.style.SUCCESS('API documentation: OK')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'API documentation returned: {response.status_code}')
                )

        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'API endpoint check failed: {str(e)}')
            )

    def check_environment(self):
        """Check environment configuration"""
        self.stdout.write('Checking environment configuration...')

        required_settings = [
            'SECRET_KEY',
            'DB_NAME',
            'DB_USERNAME',
            'DB_HOST',
        ]

        missing_settings = []
        for setting in required_settings:
            if not os.environ.get(setting):
                missing_settings.append(setting)

        if missing_settings:
            self.stdout.write(
                self.style.WARNING(
                    f'Missing environment variables: {", ".join(missing_settings)}'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('Environment configuration: OK')
            )
