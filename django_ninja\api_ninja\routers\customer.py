from ninja import Router, Query
from django.shortcuts import get_object_or_404
from django.http import Http404
from django.db.models import Q
from typing import List, Optional

# Import models from the original server app
try:
    from api.models.customer import Customer
except ImportError:
    Customer = None

from ..schemas import (
    CustomerSchema,
    CustomerListSchema,
    CustomerCreateSchema,
    CustomerSearchSchema,
    ErrorSchema
)

router = Router()

@router.get("/", response=CustomerListSchema)
def list_customers(
    request,
    limit: int = Query(100, description="Number of results per page"),
    offset: int = Query(0, description="Number of results to skip"),
    match: Optional[str] = Query(None, description="Search customer name or number"),
    customer_set_code: Optional[str] = Query(None, description="Filter by customer set code"),
    segment: Optional[str] = Query(None, description="Filter by customer segment"),
    customer_type: Optional[str] = Query(None, description="Filter by customer type")
):
    """List customers with pagination and filtering"""
    if Customer is None:
        return {"count": 0, "results": [], "next": None, "previous": None}

    queryset = Customer.objects.all()

    # Apply filters
    if match:
        queryset = queryset.filter(
            Q(full_name__icontains=match) |
            Q(customer_number__icontains=match)
        )

    if customer_set_code:
        queryset = queryset.filter(customer_set_code__icontains=customer_set_code)

    if segment:
        queryset = queryset.filter(segment__icontains=segment)

    if customer_type:
        queryset = queryset.filter(customer_type__icontains=customer_type)

    # Get total count
    total_count = queryset.count()

    # Apply pagination
    customers = queryset[offset:offset + limit]

    # Convert to schema
    results = []
    for customer in customers:
        try:
            customer_data = {
                "id": customer.customer_sk,
                "customer_sk": customer.customer_sk,
                "customer_number": customer.customer_number,
                "full_name": customer.full_name,
                "customer_type": customer.customer_type,
                "customer_set_code": customer.customer_set_code,
                "segment": customer.segment,
                "created_at": customer.created_date if hasattr(customer, 'created_date') else None,
                "updated_at": customer.updated_date if hasattr(customer, 'updated_date') else None,
            }

            results.append(CustomerSchema(**customer_data))
        except Exception as e:
            print(f"Error processing customer {customer.customer_sk}: {e}")
            continue

    # Calculate pagination info
    next_url = None
    previous_url = None

    if offset + limit < total_count:
        next_url = f"?limit={limit}&offset={offset + limit}"
        if match:
            next_url += f"&match={match}"
        if customer_set_code:
            next_url += f"&customer_set_code={customer_set_code}"
        if segment:
            next_url += f"&segment={segment}"
        if customer_type:
            next_url += f"&customer_type={customer_type}"

    if offset > 0:
        prev_offset = max(0, offset - limit)
        previous_url = f"?limit={limit}&offset={prev_offset}"
        if match:
            previous_url += f"&match={match}"
        if customer_set_code:
            previous_url += f"&customer_set_code={customer_set_code}"
        if segment:
            previous_url += f"&segment={segment}"
        if customer_type:
            previous_url += f"&customer_type={customer_type}"

    return {
        "count": total_count,
        "next": next_url,
        "previous": previous_url,
        "results": results
    }

@router.get("/{customer_sk}/", response=CustomerSchema)
def get_customer(request, customer_sk: int):
    """Get a specific customer by SK"""
    if Customer is None:
        raise Http404("Customer model not available")

    try:
        customer = Customer.objects.get(customer_sk=customer_sk)

        customer_data = {
            "id": customer.customer_sk,
            "customer_sk": customer.customer_sk,
            "customer_number": customer.customer_number,
            "full_name": customer.full_name,
            "customer_type": customer.customer_type,
            "customer_set_code": customer.customer_set_code,
            "segment": customer.segment,
            "created_at": customer.created_date if hasattr(customer, 'created_date') else None,
            "updated_at": customer.updated_date if hasattr(customer, 'updated_date') else None,
        }

        return CustomerSchema(**customer_data)

    except Customer.DoesNotExist:
        raise Http404("Customer not found")

@router.post("/", response=CustomerSchema)
def create_customer(request, payload: CustomerCreateSchema):
    """Create a new customer"""
    if Customer is None:
        raise Http404("Customer model not available")

    try:
        # Create the customer
        customer_data = payload.dict(exclude_unset=True)
        customer = Customer.objects.create(**customer_data)

        return CustomerSchema(
            id=customer.customer_sk,
            customer_sk=customer.customer_sk,
            customer_number=customer.customer_number,
            full_name=customer.full_name,
            customer_type=customer.customer_type,
            customer_set_code=customer.customer_set_code,
            segment=customer.segment,
            created_at=customer.created_date if hasattr(customer, 'created_date') else None,
            updated_at=customer.updated_date if hasattr(customer, 'updated_date') else None,
        )

    except Exception as e:
        raise Http404(f"Error creating customer: {str(e)}")

@router.get("/search/", response=CustomerListSchema)
def search_customers(request, q: str = Query(..., description="Search query")):
    """Search customers by query string"""
    if Customer is None:
        return {"count": 0, "results": [], "next": None, "previous": None}

    queryset = Customer.objects.filter(
        Q(full_name__icontains=q) |
        Q(customer_number__icontains=q)
    )[:50]  # Limit search results

    results = []
    for customer in queryset:
        try:
            customer_data = {
                "id": customer.customer_sk,
                "customer_sk": customer.customer_sk,
                "customer_number": customer.customer_number,
                "full_name": customer.full_name,
                "customer_type": customer.customer_type,
                "customer_set_code": customer.customer_set_code,
                "segment": customer.segment,
                "created_at": customer.created_date if hasattr(customer, 'created_date') else None,
                "updated_at": customer.updated_date if hasattr(customer, 'updated_date') else None,
            }

            results.append(CustomerSchema(**customer_data))
        except Exception:
            continue

    return {
        "count": len(results),
        "next": None,
        "previous": None,
        "results": results
    }

@router.get("/by-segment/{segment}/", response=CustomerListSchema)
def get_customers_by_segment(
    request,
    segment: str,
    limit: int = Query(100, description="Number of results to return")
):
    """Get customers by segment"""
    if Customer is None:
        return {"count": 0, "results": [], "next": None, "previous": None}

    customers = Customer.objects.filter(
        segment__iexact=segment
    ).order_by('full_name')[:limit]

    results = []
    for customer in customers:
        try:
            customer_data = {
                "id": customer.customer_sk,
                "customer_sk": customer.customer_sk,
                "customer_number": customer.customer_number,
                "full_name": customer.full_name,
                "customer_type": customer.customer_type,
                "customer_set_code": customer.customer_set_code,
                "segment": customer.segment,
                "created_at": customer.created_date if hasattr(customer, 'created_date') else None,
                "updated_at": customer.updated_date if hasattr(customer, 'updated_date') else None,
            }

            results.append(CustomerSchema(**customer_data))
        except Exception:
            continue

    return {
        "count": len(results),
        "next": None,
        "previous": None,
        "results": results
    }
