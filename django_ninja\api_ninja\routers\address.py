from ninja import Router, Query
from django.shortcuts import get_object_or_404
from django.http import Http404
from django.db.models import Q
from typing import List, Optional
from django.core.paginator import Paginator

# Import models from the original server app
try:
    from api.models.common.address import Address
    from api.models.dvr import DistrictValuationRoll
    from api.models.nz.linz.title import LinzTitles
except ImportError:
    Address = None
    DistrictValuationRoll = None
    LinzTitles = None

from ..schemas import (
    AddressSchema,
    AddressListSchema,
    AddressCreateSchema,
    AddressUpdateSchema,
    AddressSearchSchema,
    ErrorSchema
)

router = Router()

@router.get("/", response=AddressListSchema)
def list_addresses(
    request,
    limit: int = Query(100, description="Number of results per page"),
    offset: int = Query(0, description="Number of results to skip"),
    search: Optional[str] = Query(None, description="Search query for address"),
    land_use: Optional[str] = Query(None, description="Filter by land use"),
    land_zone: Optional[str] = Query(None, description="Filter by land zone")
):
    """List addresses with pagination and filtering"""
    if Address is None:
        return {"count": 0, "results": [], "next": None, "previous": None}

    queryset = Address.objects.all()

    # Apply filters
    if search:
        queryset = queryset.filter(
            Q(full_address__icontains=search) |
            Q(address_id__icontains=search)
        )

    if land_use:
        queryset = queryset.filter(
            district_valuation_roll__land_use_desc__icontains=land_use
        )

    if land_zone:
        queryset = queryset.filter(
            district_valuation_roll__land_zone__icontains=land_zone
        )

    # Get total count
    total_count = queryset.count()

    # Apply pagination
    addresses = queryset.select_related(
        'district_valuation_roll',
        'linz_address'
    )[offset:offset + limit]

    # Convert to schema
    results = []
    for addr in addresses:
        try:
            # Get related data
            dvr = addr.district_valuation_roll

            address_data = {
                "id": addr.address_id,
                "address_id": addr.address_id,
                "full_address": addr.full_address,
                "created_at": addr.created_date,
                "updated_at": addr.updated_date,
            }

            # Add coordinates if available
            if addr.geom:
                address_data.update({
                    "lat": addr.geom.y if addr.geom else None,
                    "lng": addr.geom.x if addr.geom else None,
                })

            # Add DVR data if available
            if dvr:
                address_data.update({
                    "land_area": dvr.land_area,
                    "land_zone": dvr.land_zone,
                    "land_use": dvr.land_use_desc,
                    "cv": dvr.capital_value,
                    "iv": dvr.improvements_value,
                    "lv": dvr.land_value,
                    "owners": dvr.owners,
                    "mortgagee": dvr.mortgagee,
                })

            results.append(AddressSchema(**address_data))
        except Exception as e:
            # Skip problematic records but log the error
            print(f"Error processing address {addr.address_id}: {e}")
            continue

    # Calculate pagination info
    next_url = None
    previous_url = None

    if offset + limit < total_count:
        next_url = f"?limit={limit}&offset={offset + limit}"
        if search:
            next_url += f"&search={search}"
        if land_use:
            next_url += f"&land_use={land_use}"
        if land_zone:
            next_url += f"&land_zone={land_zone}"

    if offset > 0:
        prev_offset = max(0, offset - limit)
        previous_url = f"?limit={limit}&offset={prev_offset}"
        if search:
            previous_url += f"&search={search}"
        if land_use:
            previous_url += f"&land_use={land_use}"
        if land_zone:
            previous_url += f"&land_zone={land_zone}"

    return {
        "count": total_count,
        "next": next_url,
        "previous": previous_url,
        "results": results
    }

@router.get("/{address_id}/", response=AddressSchema)
def get_address(request, address_id: int):
    """Get a specific address by ID"""
    if Address is None:
        raise Http404("Address model not available")

    try:
        address = Address.objects.select_related(
            'district_valuation_roll',
            'linz_address'
        ).get(address_id=address_id)

        dvr = address.district_valuation_roll

        address_data = {
            "id": address.address_id,
            "address_id": address.address_id,
            "full_address": address.full_address,
            "created_at": address.created_date,
            "updated_at": address.updated_date,
        }

        # Add coordinates if available
        if address.geom:
            address_data.update({
                "lat": address.geom.y if address.geom else None,
                "lng": address.geom.x if address.geom else None,
            })

        # Add DVR data if available
        if dvr:
            address_data.update({
                "land_area": dvr.land_area,
                "land_zone": dvr.land_zone,
                "land_use": dvr.land_use_desc,
                "cv": dvr.capital_value,
                "iv": dvr.improvements_value,
                "lv": dvr.land_value,
                "owners": dvr.owners,
                "mortgagee": dvr.mortgagee,
            })

        return AddressSchema(**address_data)

    except Address.DoesNotExist:
        raise Http404("Address not found")

@router.post("/", response=AddressSchema)
def create_address(request, payload: AddressCreateSchema):
    """Create a new address"""
    if Address is None:
        raise Http404("Address model not available")

    try:
        # Create the address
        address_data = payload.dict(exclude_unset=True)

        # Handle coordinates
        lat = address_data.pop('lat', None)
        lng = address_data.pop('lng', None)

        address = Address.objects.create(**address_data)

        # Set geometry if coordinates provided
        if lat and lng:
            from django.contrib.gis.geos import Point
            address.geom = Point(lng, lat, srid=4326)
            address.save()

        return AddressSchema(
            id=address.address_id,
            address_id=address.address_id,
            full_address=address.full_address,
            lat=lat,
            lng=lng,
            created_at=address.created_date,
            updated_at=address.updated_date,
        )

    except Exception as e:
        raise Http404(f"Error creating address: {str(e)}")

@router.put("/{address_id}/", response=AddressSchema)
def update_address(request, address_id: int, payload: AddressUpdateSchema):
    """Update an existing address"""
    if Address is None:
        raise Http404("Address model not available")

    try:
        address = Address.objects.get(address_id=address_id)

        # Update fields
        update_data = payload.dict(exclude_unset=True)
        lat = update_data.pop('lat', None)
        lng = update_data.pop('lng', None)

        for field, value in update_data.items():
            setattr(address, field, value)

        # Update geometry if coordinates provided
        if lat and lng:
            from django.contrib.gis.geos import Point
            address.geom = Point(lng, lat, srid=4326)

        address.save()

        return AddressSchema(
            id=address.address_id,
            address_id=address.address_id,
            full_address=address.full_address,
            lat=lat or (address.geom.y if address.geom else None),
            lng=lng or (address.geom.x if address.geom else None),
            created_at=address.created_date,
            updated_at=address.updated_date,
        )

    except Address.DoesNotExist:
        raise Http404("Address not found")

@router.delete("/{address_id}/")
def delete_address(request, address_id: int):
    """Delete an address"""
    if Address is None:
        raise Http404("Address model not available")

    try:
        address = Address.objects.get(address_id=address_id)
        address.delete()
        return {"success": True, "message": "Address deleted successfully"}
    except Address.DoesNotExist:
        raise Http404("Address not found")

@router.get("/search/", response=AddressListSchema)
def search_addresses(request, q: str = Query(..., description="Search query")):
    """Search addresses by query string"""
    if Address is None:
        return {"count": 0, "results": [], "next": None, "previous": None}

    queryset = Address.objects.filter(
        Q(full_address__icontains=q) |
        Q(address_id__icontains=q)
    ).select_related('district_valuation_roll')[:50]  # Limit search results

    results = []
    for addr in queryset:
        try:
            dvr = addr.district_valuation_roll
            address_data = {
                "id": addr.address_id,
                "address_id": addr.address_id,
                "full_address": addr.full_address,
                "created_at": addr.created_date,
                "updated_at": addr.updated_date,
            }

            if addr.geom:
                address_data.update({
                    "lat": addr.geom.y,
                    "lng": addr.geom.x,
                })

            if dvr:
                address_data.update({
                    "land_area": dvr.land_area,
                    "cv": dvr.capital_value,
                    "owners": dvr.owners,
                })

            results.append(AddressSchema(**address_data))
        except Exception:
            continue

    return {
        "count": len(results),
        "next": None,
        "previous": None,
        "results": results
    }
