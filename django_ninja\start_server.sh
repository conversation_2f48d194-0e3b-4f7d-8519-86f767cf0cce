#!/bin/bash

echo "🚀 Starting Django Ninja Backend Server..."
echo "=========================================="

# Set environment variables
export DJANGO_SETTINGS_MODULE=esgis_ninja.settings.development
export DEBUG=True
export DB_NAME=agrigis_dev
export DB_USERNAME=postgres
export DB_PASSWORD=postgres
export DB_HOST=localhost
export DB_PORT=5432

echo "Environment: $DJANGO_SETTINGS_MODULE"
echo "Database: $DB_NAME @ $DB_HOST:$DB_PORT"
echo ""

echo "🌐 Available URLs:"
echo "   API Documentation: http://localhost:8000/api/docs/"
echo "   Health Check: http://localhost:8000/api/health"
echo "   Admin Interface: http://localhost:8000/admin/"
echo "   Frontend: http://localhost:8000/"
echo ""

echo "🔧 Starting server..."
python manage.py runserver 0.0.0.0:8000
