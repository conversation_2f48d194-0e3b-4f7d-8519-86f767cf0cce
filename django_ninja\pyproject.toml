[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "esgis-ninja"
version = "1.0.0"
description = "ESGIS Django Ninja Backend"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "ESGIS Team", email = "<EMAIL>"},
]
keywords = ["django", "ninja", "api", "esgis", "gis"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Framework :: Django",
    "Framework :: Django :: 4.2",
]

dependencies = [
    # Django and core dependencies
    "Django>=4.2.0,<5.0",
    "django-ninja>=1.0.0",
    "pydantic>=2.0.0",
    
    # Database and GIS
    "psycopg2-binary>=2.9.0",
    "django-contrib-gis>=4.2.0",
    
    # API and serialization
    "djangorestframework>=3.14.0",
    "django-cors-headers>=4.0.0",
    "django-filter>=23.0.0",
    
    # Authentication and security
    "django-auth-ldap>=4.0.0",
    "django-axes>=6.0.0",
    
    # Utilities
    "django-extensions>=3.2.0",
    "django-safedelete>=1.3.0",
    "django-simple-history>=3.4.0",
    "django-advanced-filters>=2.0.0",
    "whitenoise>=6.5.0",
    
    # Background tasks
    "django-rq>=2.8.0",
    "redis>=4.5.0",
    
    # Storage and file handling
    "boto3>=1.26.0",
    "django-storages>=1.13.0",
    
    # Data processing
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    
    # Logging and monitoring
    "structlog>=23.0.0",
    
    # Development and testing
    "pytest>=7.0.0",
    "pytest-django>=4.5.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "isort>=5.12.0",
]

[project.optional-dependencies]
dev = [
    "django-debug-toolbar>=4.0.0",
    "django-silk>=5.0.0",
    "ipython>=8.0.0",
    "jupyter>=1.0.0",
]

test = [
    "pytest>=7.0.0",
    "pytest-django>=4.5.0",
    "pytest-cov>=4.0.0",
    "factory-boy>=3.2.0",
    "faker>=18.0.0",
]

production = [
    "gunicorn>=20.1.0",
    "uvicorn>=0.22.0",
    "sentry-sdk>=1.25.0",
]

[project.urls]
Homepage = "https://github.com/anz/esgis-ninja"
Documentation = "https://esgis-ninja.readthedocs.io/"
Repository = "https://github.com/anz/esgis-ninja.git"
"Bug Tracker" = "https://github.com/anz/esgis-ninja/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["esgis_ninja*", "api_ninja*", "green_ninja*", "finance_ninja*", "ccra_ninja*", "propertyflow_ninja*", "riskradar_ninja*"]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_django = "django"
known_first_party = ["esgis_ninja", "api_ninja", "green_ninja", "finance_ninja", "ccra_ninja", "propertyflow_ninja", "riskradar_ninja"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "DJANGO", "FIRSTPARTY", "LOCALFOLDER"]

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "esgis_ninja.settings.development"
python_files = ["tests.py", "test_*.py", "*_tests.py"]
addopts = "--cov --cov-report=html --cov-report=term-missing --cov-fail-under=80"

[tool.coverage.run]
source = "."
omit = [
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
    "manage.py",
    "*/settings/*",
    "*/tests/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
]
