from ninja import Router, Query
from django.shortcuts import get_object_or_404
from django.http import Http404
from django.db.models import Q
from typing import List, Optional
from datetime import date, datetime

# Import models from the original server app
try:
    from api.models.sale import Sale
    from api.models.common.address import Address
except ImportError:
    Sale = None
    Address = None

from ..schemas import (
    SaleSchema, 
    SaleListSchema, 
    SaleCreateSchema, 
    SaleUpdateSchema,
    SaleSearchSchema,
    AddressSchema,
    ErrorSchema
)

router = Router()

@router.get("/", response=SaleListSchema)
def list_sales(
    request,
    limit: int = Query(100, description="Number of results per page"),
    offset: int = Query(0, description="Number of results to skip"),
    address_id: Optional[int] = Query(None, description="Filter by address ID"),
    sale_date_from: Optional[date] = Query(None, description="Filter sales from this date"),
    sale_date_to: Optional[date] = Query(None, description="Filter sales to this date"),
    min_price: Optional[float] = Query(None, description="Minimum sale price"),
    max_price: Optional[float] = Query(None, description="Maximum sale price"),
    source: Optional[str] = Query(None, description="Filter by sale source")
):
    """List sales with pagination and filtering"""
    if Sale is None:
        return {"count": 0, "results": [], "next": None, "previous": None}
    
    queryset = Sale.objects.select_related('address').all()
    
    # Apply filters
    if address_id:
        queryset = queryset.filter(address__address_id=address_id)
    
    if sale_date_from:
        queryset = queryset.filter(sale_date__gte=sale_date_from)
    
    if sale_date_to:
        queryset = queryset.filter(sale_date__lte=sale_date_to)
    
    if min_price:
        queryset = queryset.filter(gross_sales_price__gte=min_price)
    
    if max_price:
        queryset = queryset.filter(gross_sales_price__lte=max_price)
    
    if source:
        queryset = queryset.filter(source__icontains=source)
    
    # Get total count
    total_count = queryset.count()
    
    # Apply pagination
    sales = queryset[offset:offset + limit]
    
    # Convert to schema
    results = []
    for sale in sales:
        try:
            sale_data = {
                "id": sale.sale_id,
                "sale_id": sale.sale_id,
                "address_id": sale.address.address_id if sale.address else None,
                "sale_date": sale.sale_date,
                "gross_sales_price": sale.gross_sales_price,
                "improvements_value": sale.improvements_value,
                "total_ha": sale.total_ha,
                "vendor": sale.vendor,
                "purchaser": sale.purchaser,
                "source": sale.source,
                "created_at": sale.created_date if hasattr(sale, 'created_date') else None,
                "updated_at": sale.updated_date if hasattr(sale, 'updated_date') else None,
            }
            
            # Add address data if available
            if sale.address:
                address_data = {
                    "id": sale.address.address_id,
                    "address_id": sale.address.address_id,
                    "full_address": sale.address.full_address,
                    "created_at": sale.address.created_date,
                    "updated_at": sale.address.updated_date,
                }
                
                if sale.address.geom:
                    address_data.update({
                        "lat": sale.address.geom.y,
                        "lng": sale.address.geom.x,
                    })
                
                sale_data["address"] = AddressSchema(**address_data)
            
            results.append(SaleSchema(**sale_data))
        except Exception as e:
            print(f"Error processing sale {sale.sale_id}: {e}")
            continue
    
    # Calculate pagination info
    next_url = None
    previous_url = None
    
    if offset + limit < total_count:
        next_url = f"?limit={limit}&offset={offset + limit}"
        if address_id:
            next_url += f"&address_id={address_id}"
        if sale_date_from:
            next_url += f"&sale_date_from={sale_date_from}"
        if sale_date_to:
            next_url += f"&sale_date_to={sale_date_to}"
        if min_price:
            next_url += f"&min_price={min_price}"
        if max_price:
            next_url += f"&max_price={max_price}"
        if source:
            next_url += f"&source={source}"
    
    if offset > 0:
        prev_offset = max(0, offset - limit)
        previous_url = f"?limit={limit}&offset={prev_offset}"
        if address_id:
            previous_url += f"&address_id={address_id}"
        if sale_date_from:
            previous_url += f"&sale_date_from={sale_date_from}"
        if sale_date_to:
            previous_url += f"&sale_date_to={sale_date_to}"
        if min_price:
            previous_url += f"&min_price={min_price}"
        if max_price:
            previous_url += f"&max_price={max_price}"
        if source:
            previous_url += f"&source={source}"
    
    return {
        "count": total_count,
        "next": next_url,
        "previous": previous_url,
        "results": results
    }

@router.get("/{sale_id}", response=SaleSchema)
def get_sale(request, sale_id: int):
    """Get a specific sale by ID"""
    if Sale is None:
        raise Http404("Sale model not available")
    
    try:
        sale = Sale.objects.select_related('address').get(sale_id=sale_id)
        
        sale_data = {
            "id": sale.sale_id,
            "sale_id": sale.sale_id,
            "address_id": sale.address.address_id if sale.address else None,
            "sale_date": sale.sale_date,
            "gross_sales_price": sale.gross_sales_price,
            "improvements_value": sale.improvements_value,
            "total_ha": sale.total_ha,
            "vendor": sale.vendor,
            "purchaser": sale.purchaser,
            "source": sale.source,
            "created_at": sale.created_date if hasattr(sale, 'created_date') else None,
            "updated_at": sale.updated_date if hasattr(sale, 'updated_date') else None,
        }
        
        # Add address data if available
        if sale.address:
            address_data = {
                "id": sale.address.address_id,
                "address_id": sale.address.address_id,
                "full_address": sale.address.full_address,
                "created_at": sale.address.created_date,
                "updated_at": sale.address.updated_date,
            }
            
            if sale.address.geom:
                address_data.update({
                    "lat": sale.address.geom.y,
                    "lng": sale.address.geom.x,
                })
            
            sale_data["address"] = AddressSchema(**address_data)
        
        return SaleSchema(**sale_data)
        
    except Sale.DoesNotExist:
        raise Http404("Sale not found")

@router.post("/", response=SaleSchema)
def create_sale(request, payload: SaleCreateSchema):
    """Create a new sale"""
    if Sale is None or Address is None:
        raise Http404("Sale or Address model not available")
    
    try:
        # Validate address exists
        address = Address.objects.get(address_id=payload.address_id)
        
        # Create the sale
        sale_data = payload.dict(exclude_unset=True)
        sale = Sale.objects.create(
            address=address,
            **{k: v for k, v in sale_data.items() if k != 'address_id'}
        )
        
        return SaleSchema(
            id=sale.sale_id,
            sale_id=sale.sale_id,
            address_id=address.address_id,
            sale_date=sale.sale_date,
            gross_sales_price=sale.gross_sales_price,
            improvements_value=sale.improvements_value,
            total_ha=sale.total_ha,
            vendor=sale.vendor,
            purchaser=sale.purchaser,
            source=sale.source,
            created_at=sale.created_date if hasattr(sale, 'created_date') else None,
            updated_at=sale.updated_date if hasattr(sale, 'updated_date') else None,
        )
        
    except Address.DoesNotExist:
        raise Http404("Address not found")
    except Exception as e:
        raise Http404(f"Error creating sale: {str(e)}")

@router.put("/{sale_id}", response=SaleSchema)
def update_sale(request, sale_id: int, payload: SaleUpdateSchema):
    """Update an existing sale"""
    if Sale is None:
        raise Http404("Sale model not available")
    
    try:
        sale = Sale.objects.select_related('address').get(sale_id=sale_id)
        
        # Update fields
        update_data = payload.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(sale, field, value)
        
        sale.save()
        
        sale_data = {
            "id": sale.sale_id,
            "sale_id": sale.sale_id,
            "address_id": sale.address.address_id if sale.address else None,
            "sale_date": sale.sale_date,
            "gross_sales_price": sale.gross_sales_price,
            "improvements_value": sale.improvements_value,
            "total_ha": sale.total_ha,
            "vendor": sale.vendor,
            "purchaser": sale.purchaser,
            "source": sale.source,
            "created_at": sale.created_date if hasattr(sale, 'created_date') else None,
            "updated_at": sale.updated_date if hasattr(sale, 'updated_date') else None,
        }
        
        return SaleSchema(**sale_data)
        
    except Sale.DoesNotExist:
        raise Http404("Sale not found")

@router.delete("/{sale_id}")
def delete_sale(request, sale_id: int):
    """Delete a sale"""
    if Sale is None:
        raise Http404("Sale model not available")
    
    try:
        sale = Sale.objects.get(sale_id=sale_id)
        sale.delete()
        return {"success": True, "message": "Sale deleted successfully"}
    except Sale.DoesNotExist:
        raise Http404("Sale not found")

@router.get("/by-address/{address_id}", response=SaleListSchema)
def get_sales_by_address(
    request, 
    address_id: int, 
    limit: int = Query(100, description="Number of results to return")
):
    """Get sales for a specific address"""
    if Sale is None:
        return {"count": 0, "results": [], "next": None, "previous": None}
    
    sales = Sale.objects.filter(
        address__address_id=address_id
    ).select_related('address').order_by('-sale_date')[:limit]
    
    results = []
    for sale in sales:
        try:
            sale_data = {
                "id": sale.sale_id,
                "sale_id": sale.sale_id,
                "address_id": sale.address.address_id if sale.address else None,
                "sale_date": sale.sale_date,
                "gross_sales_price": sale.gross_sales_price,
                "improvements_value": sale.improvements_value,
                "total_ha": sale.total_ha,
                "vendor": sale.vendor,
                "purchaser": sale.purchaser,
                "source": sale.source,
                "created_at": sale.created_date if hasattr(sale, 'created_date') else None,
                "updated_at": sale.updated_date if hasattr(sale, 'updated_date') else None,
            }
            
            results.append(SaleSchema(**sale_data))
        except Exception:
            continue
    
    return {
        "count": len(results),
        "next": None,
        "previous": None,
        "results": results
    }
