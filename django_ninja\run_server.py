#!/usr/bin/env python
"""
Django Ninja development server runner
"""
import os
import sys
import subprocess
from pathlib import Path


def main():
    """Run the Django Ninja development server"""
    
    # Set default Django settings module
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esgis_ninja.settings.development')
    
    # Add current directory to Python path
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))
    
    print("🚀 Starting Django Ninja Backend Server...")
    print("=" * 50)
    
    # Check if manage.py exists
    manage_py = current_dir / 'manage.py'
    if not manage_py.exists():
        print("❌ Error: manage.py not found!")
        sys.exit(1)
    
    # Set environment variables if not already set
    env_vars = {
        'DEBUG': 'True',
        'DB_NAME': 'agrigis_dev',
        'DB_USERNAME': 'postgres',
        'DB_PASSWORD': 'postgres',
        'DB_HOST': 'localhost',
        'DB_PORT': '5432',
        'REDIS_HOST': 'localhost',
        'REDIS_PORT': '6379',
    }
    
    for key, value in env_vars.items():
        if key not in os.environ:
            os.environ[key] = value
            print(f"🔧 Set {key}={value}")
    
    print("\n📋 Server Information:")
    print(f"   Settings: {os.environ.get('DJANGO_SETTINGS_MODULE')}")
    print(f"   Database: {os.environ.get('DB_NAME')} @ {os.environ.get('DB_HOST')}:{os.environ.get('DB_PORT')}")
    print(f"   Debug: {os.environ.get('DEBUG')}")
    
    print("\n🌐 Available URLs:")
    print("   API Documentation: http://localhost:8000/api/docs/")
    print("   Health Check: http://localhost:8000/api/health")
    print("   Admin Interface: http://localhost:8000/admin/")
    print("   Frontend: http://localhost:8000/")
    
    print("\n" + "=" * 50)
    
    try:
        # Run Django development server
        subprocess.run([
            sys.executable, 
            str(manage_py), 
            'runserver', 
            '0.0.0.0:8000'
        ], check=True)
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Server failed to start: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
