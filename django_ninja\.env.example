# Django Settings
DJANGO_SETTINGS_MODULE=esgis_ninja.settings.development
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DB_NAME=agrigis_dev
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# LDAP Configuration (Production only)
LDAP_BIND_DN=
LDAP_BIND_PASSWORD=
LDAP_SERVER_URI=ldaps://global-ldap.lb.apps.anz:636

# S3 Configuration
S3_IIH_INBOUND_ACCESS_KEY_ID=
S3_IIH_INBOUND_SECRET_KEY=
S3_IIH_INBOUND_BUCKET_NAME=
S3_IIH_INBOUND_HOST=

# Teradata Configuration
TERADATA_CONNECTION_STRING=

# Application Configuration
BASE_URL=http://localhost:8000
ANONYMIZE_VALUES=False

# Email Configuration (Production)
EMAIL_HOST=smtp.anz.com
EMAIL_PORT=587
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
