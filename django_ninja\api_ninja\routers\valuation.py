from ninja import Router, Query
from django.shortcuts import get_object_or_404
from django.http import Http404
from django.db.models import Q
from typing import List, Optional
from datetime import date
from decimal import Decimal

# Import models from the original server app
try:
    from api.models.valuation import Valuation
    from api.models.common.address import Address
    from django.contrib.auth.models import User
except ImportError:
    Valuation = None
    Address = None
    User = None

from ..schemas import (
    ValuationSchema,
    ValuationListSchema,
    ValuationCreateSchema,
    ValuationUpdateSchema,
    ValuationSearchSchema,
    AddressSchema,
    ErrorSchema
)

router = Router()

@router.get("/", response=ValuationListSchema)
def list_valuations(
    request,
    limit: int = Query(100, description="Number of results per page"),
    offset: int = Query(0, description="Number of results to skip"),
    address_id: Optional[int] = Query(None, description="Filter by address ID"),
    valuation_date_from: Optional[date] = Query(None, description="Filter valuations from this date"),
    valuation_date_to: Optional[date] = Query(None, description="Filter valuations to this date"),
    min_value: Optional[Decimal] = Query(None, description="Minimum valuation value"),
    max_value: Optional[Decimal] = Query(None, description="Maximum valuation value"),
    approach: Optional[str] = Query(None, description="Filter by valuation approach"),
    tier: Optional[int] = Query(None, description="Filter by property tier")
):
    """List valuations with pagination and filtering"""
    if Valuation is None:
        return {"count": 0, "results": [], "next": None, "previous": None}

    queryset = Valuation.objects.select_related('address', 'creator', 'assigned', 'approver').all()

    # Apply filters
    if address_id:
        queryset = queryset.filter(address__address_id=address_id)

    if valuation_date_from:
        queryset = queryset.filter(valuation_date__gte=valuation_date_from)

    if valuation_date_to:
        queryset = queryset.filter(valuation_date__lte=valuation_date_to)

    if min_value:
        queryset = queryset.filter(total_value__gte=min_value)

    if max_value:
        queryset = queryset.filter(total_value__lte=max_value)

    if approach:
        queryset = queryset.filter(approach__icontains=approach)

    if tier:
        queryset = queryset.filter(tier=tier)

    # Get total count
    total_count = queryset.count()

    # Apply pagination
    valuations = queryset[offset:offset + limit]

    # Convert to schema
    results = []
    for valuation in valuations:
        try:
            valuation_data = {
                "id": valuation.id,
                "valuation_reference": valuation.valuation_reference,
                "valuation_name": valuation.valuation_name,
                "address_id": valuation.address.address_id if valuation.address else None,
                "valuation_date": valuation.valuation_date,
                "total_value": valuation.total_value,
                "approach": valuation.approach,
                "tier": valuation.tier,
                "ccr": valuation.ccr,
                "lvr": valuation.lvr,
                "creator_id": valuation.creator.id if valuation.creator else None,
                "assigned_id": valuation.assigned.id if valuation.assigned else None,
                "approver_id": valuation.approver.id if valuation.approver else None,
                "created_at": valuation.created_date if hasattr(valuation, 'created_date') else None,
                "updated_at": valuation.updated_date if hasattr(valuation, 'updated_date') else None,
            }

            # Add address data if available
            if valuation.address:
                address_data = {
                    "id": valuation.address.address_id,
                    "address_id": valuation.address.address_id,
                    "full_address": valuation.address.full_address,
                    "created_at": valuation.address.created_date,
                    "updated_at": valuation.address.updated_date,
                }

                if valuation.address.geom:
                    address_data.update({
                        "lat": valuation.address.geom.y,
                        "lng": valuation.address.geom.x,
                    })

                valuation_data["address"] = AddressSchema(**address_data)

            results.append(ValuationSchema(**valuation_data))
        except Exception as e:
            print(f"Error processing valuation {valuation.id}: {e}")
            continue

    # Calculate pagination info
    next_url = None
    previous_url = None

    if offset + limit < total_count:
        next_url = f"?limit={limit}&offset={offset + limit}"
        if address_id:
            next_url += f"&address_id={address_id}"
        if valuation_date_from:
            next_url += f"&valuation_date_from={valuation_date_from}"
        if valuation_date_to:
            next_url += f"&valuation_date_to={valuation_date_to}"
        if min_value:
            next_url += f"&min_value={min_value}"
        if max_value:
            next_url += f"&max_value={max_value}"
        if approach:
            next_url += f"&approach={approach}"
        if tier:
            next_url += f"&tier={tier}"

    if offset > 0:
        prev_offset = max(0, offset - limit)
        previous_url = f"?limit={limit}&offset={prev_offset}"
        if address_id:
            previous_url += f"&address_id={address_id}"
        if valuation_date_from:
            previous_url += f"&valuation_date_from={valuation_date_from}"
        if valuation_date_to:
            previous_url += f"&valuation_date_to={valuation_date_to}"
        if min_value:
            previous_url += f"&min_value={min_value}"
        if max_value:
            previous_url += f"&max_value={max_value}"
        if approach:
            previous_url += f"&approach={approach}"
        if tier:
            previous_url += f"&tier={tier}"

    return {
        "count": total_count,
        "next": next_url,
        "previous": previous_url,
        "results": results
    }

@router.get("/{valuation_id}/", response=ValuationSchema)
def get_valuation(request, valuation_id: int):
    """Get a specific valuation by ID"""
    if Valuation is None:
        raise Http404("Valuation model not available")

    try:
        valuation = Valuation.objects.select_related(
            'address', 'creator', 'assigned', 'approver'
        ).get(id=valuation_id)

        valuation_data = {
            "id": valuation.id,
            "valuation_reference": valuation.valuation_reference,
            "valuation_name": valuation.valuation_name,
            "address_id": valuation.address.address_id if valuation.address else None,
            "valuation_date": valuation.valuation_date,
            "total_value": valuation.total_value,
            "approach": valuation.approach,
            "tier": valuation.tier,
            "ccr": valuation.ccr,
            "lvr": valuation.lvr,
            "creator_id": valuation.creator.id if valuation.creator else None,
            "assigned_id": valuation.assigned.id if valuation.assigned else None,
            "approver_id": valuation.approver.id if valuation.approver else None,
            "created_at": valuation.created_date if hasattr(valuation, 'created_date') else None,
            "updated_at": valuation.updated_date if hasattr(valuation, 'updated_date') else None,
        }

        # Add address data if available
        if valuation.address:
            address_data = {
                "id": valuation.address.address_id,
                "address_id": valuation.address.address_id,
                "full_address": valuation.address.full_address,
                "created_at": valuation.address.created_date,
                "updated_at": valuation.address.updated_date,
            }

            if valuation.address.geom:
                address_data.update({
                    "lat": valuation.address.geom.y,
                    "lng": valuation.address.geom.x,
                })

            valuation_data["address"] = AddressSchema(**address_data)

        return ValuationSchema(**valuation_data)

    except Valuation.DoesNotExist:
        raise Http404("Valuation not found")

@router.post("/", response=ValuationSchema)
def create_valuation(request, payload: ValuationCreateSchema):
    """Create a new valuation"""
    if Valuation is None or Address is None:
        raise Http404("Valuation or Address model not available")

    try:
        # Validate address exists
        address = Address.objects.get(address_id=payload.address_id)

        # Create the valuation
        valuation_data = payload.dict(exclude_unset=True)
        valuation = Valuation.objects.create(
            address=address,
            creator=request.user if request.user.is_authenticated else None,
            **{k: v for k, v in valuation_data.items() if k != 'address_id'}
        )

        return ValuationSchema(
            id=valuation.id,
            valuation_reference=valuation.valuation_reference,
            valuation_name=valuation.valuation_name,
            address_id=address.address_id,
            valuation_date=valuation.valuation_date,
            total_value=valuation.total_value,
            approach=valuation.approach,
            tier=valuation.tier,
            ccr=valuation.ccr,
            lvr=valuation.lvr,
            creator_id=valuation.creator.id if valuation.creator else None,
            created_at=valuation.created_date if hasattr(valuation, 'created_date') else None,
            updated_at=valuation.updated_date if hasattr(valuation, 'updated_date') else None,
        )

    except Address.DoesNotExist:
        raise Http404("Address not found")
    except Exception as e:
        raise Http404(f"Error creating valuation: {str(e)}")

@router.put("/{valuation_id}/", response=ValuationSchema)
def update_valuation(request, valuation_id: int, payload: ValuationUpdateSchema):
    """Update an existing valuation"""
    if Valuation is None:
        raise Http404("Valuation model not available")

    try:
        valuation = Valuation.objects.select_related('address').get(id=valuation_id)

        # Update fields
        update_data = payload.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(valuation, field, value)

        valuation.save()

        valuation_data = {
            "id": valuation.id,
            "valuation_reference": valuation.valuation_reference,
            "valuation_name": valuation.valuation_name,
            "address_id": valuation.address.address_id if valuation.address else None,
            "valuation_date": valuation.valuation_date,
            "total_value": valuation.total_value,
            "approach": valuation.approach,
            "tier": valuation.tier,
            "ccr": valuation.ccr,
            "lvr": valuation.lvr,
            "creator_id": valuation.creator.id if valuation.creator else None,
            "assigned_id": valuation.assigned.id if valuation.assigned else None,
            "approver_id": valuation.approver.id if valuation.approver else None,
            "created_at": valuation.created_date if hasattr(valuation, 'created_date') else None,
            "updated_at": valuation.updated_date if hasattr(valuation, 'updated_date') else None,
        }

        return ValuationSchema(**valuation_data)

    except Valuation.DoesNotExist:
        raise Http404("Valuation not found")

@router.delete("/{valuation_id}/")
def delete_valuation(request, valuation_id: int):
    """Delete a valuation"""
    if Valuation is None:
        raise Http404("Valuation model not available")

    try:
        valuation = Valuation.objects.get(id=valuation_id)
        valuation.delete()
        return {"success": True, "message": "Valuation deleted successfully"}
    except Valuation.DoesNotExist:
        raise Http404("Valuation not found")

@router.get("/by-address/{address_id}/", response=ValuationListSchema)
def get_valuations_by_address(
    request,
    address_id: int,
    limit: int = Query(100, description="Number of results to return")
):
    """Get valuations for a specific address"""
    if Valuation is None:
        return {"count": 0, "results": [], "next": None, "previous": None}

    valuations = Valuation.objects.filter(
        address__address_id=address_id
    ).select_related('address').order_by('-valuation_date')[:limit]

    results = []
    for valuation in valuations:
        try:
            valuation_data = {
                "id": valuation.id,
                "valuation_reference": valuation.valuation_reference,
                "valuation_name": valuation.valuation_name,
                "address_id": valuation.address.address_id if valuation.address else None,
                "valuation_date": valuation.valuation_date,
                "total_value": valuation.total_value,
                "approach": valuation.approach,
                "tier": valuation.tier,
                "ccr": valuation.ccr,
                "lvr": valuation.lvr,
                "creator_id": valuation.creator.id if valuation.creator else None,
                "assigned_id": valuation.assigned.id if valuation.assigned else None,
                "approver_id": valuation.approver.id if valuation.approver else None,
                "created_at": valuation.created_date if hasattr(valuation, 'created_date') else None,
                "updated_at": valuation.updated_date if hasattr(valuation, 'updated_date') else None,
            }

            results.append(ValuationSchema(**valuation_data))
        except Exception:
            continue

    return {
        "count": len(results),
        "next": None,
        "previous": None,
        "results": results
    }
