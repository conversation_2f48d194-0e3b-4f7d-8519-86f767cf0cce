from django.db import models
from django.contrib.gis.db import models as gis_models

class Location(models.Model):
    """Risk location model for RiskRadar Ninja"""
    location_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255, null=True, blank=True)
    lat = models.FloatField(null=True, blank=True)
    lng = models.FloatField(null=True, blank=True)
    risk_level = models.CharField(max_length=50, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name or f"Location {self.location_id}"
    
    class Meta:
        db_table = 'riskradar_ninja_location'

class RiskAssessment(models.Model):
    """Risk assessment model for RiskRadar Ninja"""
    assessment_id = models.AutoField(primary_key=True)
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='assessments')
    assessment_date = models.DateTimeField(null=True, blank=True)
    risk_score = models.FloatField(null=True, blank=True)
    risk_category = models.CharField(max_length=50, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Assessment {self.assessment_id} for {self.location}"
    
    class Meta:
        db_table = 'riskradar_ninja_risk_assessment'