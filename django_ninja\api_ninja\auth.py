from ninja.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SessionAuth
from django.contrib.auth.models import User, AnonymousUser
from django.http import HttpRequest
from typing import Optional


class SessionAuthentication(SessionAuth):
    """Session-based authentication for Django Ninja"""
    
    def authenticate(self, request: HttpRequest, token: Optional[str]) -> Optional[User]:
        """Authenticate user using Django session"""
        if request.user.is_authenticated:
            return request.user
        return None


class OptionalSessionAuth(SessionAuth):
    """Optional session authentication - allows both authenticated and anonymous users"""
    
    def authenticate(self, request: HttpRequest, token: Optional[str]) -> Optional[User]:
        """Authenticate user using Django session, but allow anonymous access"""
        if hasattr(request, 'user'):
            return request.user
        return AnonymousUser()


# Authentication instances
session_auth = SessionAuthentication()
optional_auth = OptionalSessionAuth()


def get_current_user(request: HttpRequest) -> User:
    """Get the current authenticated user from request"""
    if hasattr(request, 'user') and request.user.is_authenticated:
        return request.user
    return AnonymousUser()


def require_authenticated_user(request: HttpRequest) -> User:
    """Require an authenticated user, raise exception if not authenticated"""
    user = get_current_user(request)
    if not user.is_authenticated:
        from django.http import Http404
        raise Http404("Authentication required")
    return user


def check_permission(user: User, permission: str) -> bool:
    """Check if user has a specific permission"""
    if not user.is_authenticated:
        return False
    return user.has_perm(permission)


def check_staff_permission(user: User) -> bool:
    """Check if user is staff"""
    if not user.is_authenticated:
        return False
    return user.is_staff


def check_superuser_permission(user: User) -> bool:
    """Check if user is superuser"""
    if not user.is_authenticated:
        return False
    return user.is_superuser


# Permission decorators for views
def require_permission(permission: str):
    """Decorator to require a specific permission"""
    def decorator(func):
        def wrapper(request, *args, **kwargs):
            user = require_authenticated_user(request)
            if not check_permission(user, permission):
                from django.http import Http404
                raise Http404("Permission denied")
            return func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_staff(func):
    """Decorator to require staff permission"""
    def wrapper(request, *args, **kwargs):
        user = require_authenticated_user(request)
        if not check_staff_permission(user):
            from django.http import Http404
            raise Http404("Staff permission required")
        return func(request, *args, **kwargs)
    return wrapper


def require_superuser(func):
    """Decorator to require superuser permission"""
    def wrapper(request, *args, **kwargs):
        user = require_authenticated_user(request)
        if not check_superuser_permission(user):
            from django.http import Http404
            raise Http404("Superuser permission required")
        return func(request, *args, **kwargs)
    return wrapper


# LDAP-specific utilities
def get_ldap_user_info(user: User) -> dict:
    """Get LDAP-specific user information if available"""
    user_info = {
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'is_staff': user.is_staff,
        'is_superuser': user.is_superuser,
        'is_active': user.is_active,
        'date_joined': user.date_joined,
        'last_login': user.last_login,
    }
    
    # Add LDAP-specific attributes if available
    if hasattr(user, 'ldap_user'):
        ldap_user = user.ldap_user
        user_info.update({
            'ldap_dn': getattr(ldap_user, 'dn', None),
            'ldap_groups': getattr(ldap_user, 'group_names', []),
        })
    
    return user_info


def get_user_permissions_info(user: User) -> dict:
    """Get comprehensive user permissions information"""
    if not user.is_authenticated:
        return {
            'permissions': [],
            'groups': [],
            'is_staff': False,
            'is_superuser': False,
        }
    
    return {
        'permissions': list(user.get_all_permissions()),
        'groups': [group.name for group in user.groups.all()],
        'is_staff': user.is_staff,
        'is_superuser': user.is_superuser,
    }


# API key authentication (for future use)
class APIKeyAuth(HttpBearer):
    """API Key authentication for external integrations"""
    
    def authenticate(self, request: HttpRequest, token: str) -> Optional[User]:
        """Authenticate using API key"""
        # TODO: Implement API key authentication
        # This would typically involve:
        # 1. Looking up the API key in a database
        # 2. Validating the key
        # 3. Returning the associated user
        return None


# Rate limiting utilities
def check_rate_limit(request: HttpRequest, key: str, limit: int, window: int) -> bool:
    """Check if request is within rate limit"""
    # TODO: Implement rate limiting using Redis
    # This would typically involve:
    # 1. Creating a key based on user/IP and endpoint
    # 2. Incrementing a counter in Redis
    # 3. Setting expiration if first request
    # 4. Checking if counter exceeds limit
    return True


def get_client_ip(request: HttpRequest) -> str:
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


# Audit logging utilities
def log_api_access(request: HttpRequest, endpoint: str, user: User = None):
    """Log API access for audit purposes"""
    import logging
    
    logger = logging.getLogger('api_access')
    
    user_info = 'anonymous'
    if user and user.is_authenticated:
        user_info = f"{user.username} ({user.id})"
    
    logger.info(
        f"API Access - User: {user_info}, "
        f"Endpoint: {endpoint}, "
        f"IP: {get_client_ip(request)}, "
        f"Method: {request.method}, "
        f"User-Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}"
    )


def log_api_error(request: HttpRequest, endpoint: str, error: str, user: User = None):
    """Log API errors for monitoring"""
    import logging
    
    logger = logging.getLogger('api_errors')
    
    user_info = 'anonymous'
    if user and user.is_authenticated:
        user_info = f"{user.username} ({user.id})"
    
    logger.error(
        f"API Error - User: {user_info}, "
        f"Endpoint: {endpoint}, "
        f"Error: {error}, "
        f"IP: {get_client_ip(request)}, "
        f"Method: {request.method}"
    )
