#!/usr/bin/env python
"""
Setup script for Django Ninja Backend
"""
import os
import sys
import subprocess
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False


def check_requirements():
    """Check if required software is installed"""
    print("🔍 Checking requirements...")
    
    requirements = [
        ("python", "Python 3.8+"),
        ("pip", "Python package installer"),
    ]
    
    missing = []
    for cmd, desc in requirements:
        try:
            subprocess.run([cmd, "--version"], capture_output=True, check=True)
            print(f"✅ {desc} is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {desc} is not installed")
            missing.append(desc)
    
    if missing:
        print(f"\n❌ Missing requirements: {', '.join(missing)}")
        return False
    
    return True


def setup_environment():
    """Set up the development environment"""
    print("\n🚀 Setting up Django Ninja Backend...")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        return False
    
    # Install dependencies
    if not run_command("pip install -e .", "Installing dependencies"):
        return False
    
    # Set environment variables
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esgis_ninja.settings.development')
    os.environ.setdefault('DEBUG', 'True')
    os.environ.setdefault('DB_NAME', 'agrigis_dev')
    os.environ.setdefault('DB_USERNAME', 'postgres')
    os.environ.setdefault('DB_PASSWORD', 'postgres')
    os.environ.setdefault('DB_HOST', 'localhost')
    os.environ.setdefault('DB_PORT', '5432')
    
    # Run Django setup commands
    commands = [
        ("python manage.py check", "Checking Django configuration"),
        ("python manage.py collectstatic --noinput", "Collecting static files"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            print(f"⚠️  Warning: {description} failed, but continuing...")
    
    # Try to run migrations (may fail if database is not set up)
    print("🔧 Attempting to run database migrations...")
    try:
        subprocess.run(["python", "manage.py", "migrate"], check=True, capture_output=True)
        print("✅ Database migrations completed successfully")
    except subprocess.CalledProcessError:
        print("⚠️  Database migrations failed - you may need to set up your database first")
        print("   Make sure PostgreSQL is running and the database exists")
    
    # Try to set up initial data
    print("🔧 Setting up initial data...")
    try:
        subprocess.run([
            "python", "manage.py", "setup_ninja", 
            "--setup-groups", 
            "--create-superuser", 
            "--username=admin", 
            "--password=admin123"
        ], check=True, capture_output=True)
        print("✅ Initial data setup completed")
    except subprocess.CalledProcessError:
        print("⚠️  Initial data setup failed - you can run this manually later")
    
    return True


def main():
    """Main setup function"""
    current_dir = Path(__file__).parent
    os.chdir(current_dir)
    
    if setup_environment():
        print("\n" + "=" * 50)
        print("🎉 Django Ninja Backend setup completed!")
        print("\n📋 Next steps:")
        print("1. Make sure PostgreSQL is running")
        print("2. Create the database: createdb agrigis_dev")
        print("3. Run migrations: python manage.py migrate")
        print("4. Start the server: python run_server.py")
        print("\n🌐 Once running, visit:")
        print("   API Documentation: http://localhost:8000/api/docs/")
        print("   Admin Interface: http://localhost:8000/admin/ (admin/admin123)")
        print("   Health Check: http://localhost:8000/api/health")
        
        # Create .env file if it doesn't exist
        env_file = current_dir / '.env'
        if not env_file.exists():
            print("\n🔧 Creating .env file...")
            with open(env_file, 'w') as f:
                f.write("""# Django Ninja Backend Environment Variables
DJANGO_SETTINGS_MODULE=esgis_ninja.settings.development
DEBUG=True
SECRET_KEY=your-secret-key-here-change-in-production

# Database Configuration
DB_NAME=agrigis_dev
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Other settings
ALLOWED_HOSTS=localhost,127.0.0.1
""")
            print("✅ Created .env file - please review and update as needed")
        
    else:
        print("\n❌ Setup failed!")
        sys.exit(1)


if __name__ == '__main__':
    main()
