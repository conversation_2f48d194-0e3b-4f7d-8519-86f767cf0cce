import pytest
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User


class APITestCase(TestCase):
    """Test cases for the Django Ninja API"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_health_check(self):
        """Test the health check endpoint"""
        response = self.client.get('/api/health')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['status'], 'ok')
        self.assertEqual(data['message'], 'API is running')
    
    def test_api_docs_accessible(self):
        """Test that API documentation is accessible"""
        response = self.client.get('/api/docs/')
        self.assertEqual(response.status_code, 200)
    
    def test_addresses_endpoint_exists(self):
        """Test that addresses endpoint exists"""
        response = self.client.get('/api/addresses/')
        # Should return 200 even if no data (empty list)
        self.assertIn(response.status_code, [200, 404])  # 404 if models not available
    
    def test_sales_endpoint_exists(self):
        """Test that sales endpoint exists"""
        response = self.client.get('/api/sales/')
        # Should return 200 even if no data (empty list)
        self.assertIn(response.status_code, [200, 404])  # 404 if models not available
    
    def test_valuations_endpoint_exists(self):
        """Test that valuations endpoint exists"""
        response = self.client.get('/api/valuations/')
        # Should return 200 even if no data (empty list)
        self.assertIn(response.status_code, [200, 404])  # 404 if models not available
    
    def test_customers_endpoint_exists(self):
        """Test that customers endpoint exists"""
        response = self.client.get('/api/customers/')
        # Should return 200 even if no data (empty list)
        self.assertIn(response.status_code, [200, 404])  # 404 if models not available
    
    def test_auth_user_endpoint(self):
        """Test the auth user endpoint"""
        # Test without authentication
        response = self.client.get('/api/auth/user')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('error', data)
        
        # Test with authentication
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get('/api/auth/user')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['username'], 'testuser')
    
    def test_module_health_checks(self):
        """Test module health check endpoints"""
        modules = ['green', 'finance', 'ccra', 'propertyflow', 'riskradar']
        
        for module in modules:
            response = self.client.get(f'/api/{module}/health')
            self.assertEqual(response.status_code, 200)
            data = response.json()
            self.assertEqual(data['status'], 'ok')
            self.assertIn(f'{module.title()} API is running', data['message'])
    
    def test_root_endpoint_serves_frontend(self):
        """Test that root endpoint serves the frontend"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn('text/html', response['Content-Type'])
        self.assertIn('ESGIS Django Ninja Backend', response.content.decode())


@pytest.mark.django_db
class APIIntegrationTest:
    """Integration tests for API functionality"""
    
    def test_api_schema_generation(self):
        """Test that API schema can be generated without errors"""
        from esgis_ninja.urls import api
        
        # This should not raise any exceptions
        schema = api.get_openapi_schema()
        assert schema is not None
        assert 'openapi' in schema
        assert 'info' in schema
        assert schema['info']['title'] == 'ESGIS API'
    
    def test_pagination_schema(self):
        """Test that pagination schemas are properly defined"""
        from api_ninja.schemas import PaginatedResponseSchema
        
        # Test that the schema can be instantiated
        paginated = PaginatedResponseSchema(
            count=0,
            next=None,
            previous=None,
            results=[]
        )
        assert paginated.count == 0
        assert paginated.results == []
    
    def test_address_schema_validation(self):
        """Test address schema validation"""
        from api_ninja.schemas import AddressCreateSchema, AddressSchema
        
        # Test valid address creation schema
        create_data = {
            "full_address": "123 Test Street, Test City",
            "lat": -36.8485,
            "lng": 174.7633
        }
        
        create_schema = AddressCreateSchema(**create_data)
        assert create_schema.full_address == "123 Test Street, Test City"
        assert create_schema.lat == -36.8485
        assert create_schema.lng == 174.7633
    
    def test_sale_schema_validation(self):
        """Test sale schema validation"""
        from api_ninja.schemas import SaleCreateSchema
        from datetime import datetime
        
        # Test valid sale creation schema
        create_data = {
            "address_id": 1,
            "sale_date": datetime.now(),
            "gross_sales_price": 500000.0,
            "vendor": "Test Vendor",
            "purchaser": "Test Purchaser"
        }
        
        create_schema = SaleCreateSchema(**create_data)
        assert create_schema.address_id == 1
        assert create_schema.gross_sales_price == 500000.0
    
    def test_valuation_schema_validation(self):
        """Test valuation schema validation"""
        from api_ninja.schemas import ValuationCreateSchema, ValuationApproach, PropertyTier
        from datetime import date
        from decimal import Decimal
        
        # Test valid valuation creation schema
        create_data = {
            "address_id": 1,
            "valuation_date": date.today(),
            "total_value": Decimal("750000.00"),
            "approach": ValuationApproach.MARKET,
            "tier": PropertyTier.TIER_1
        }
        
        create_schema = ValuationCreateSchema(**create_data)
        assert create_schema.address_id == 1
        assert create_schema.total_value == Decimal("750000.00")
        assert create_schema.approach == ValuationApproach.MARKET
