from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from decimal import Decimal
from enum import Enum

# Base schemas
class HealthSchema(BaseModel):
    status: str
    message: str

class BaseResponseSchema(BaseModel):
    """Base response schema with common fields"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class PaginatedResponseSchema(BaseModel):
    """Base paginated response schema"""
    count: int
    next: Optional[str] = None
    previous: Optional[str] = None
    results: List[Any]

# Address schemas
class AddressSchema(BaseResponseSchema):
    """Address schema for API responses"""
    address_id: int
    full_address: Optional[str] = None
    lat: Optional[float] = None
    lng: Optional[float] = None
    land_area: Optional[float] = None
    land_zone: Optional[str] = None
    land_use: Optional[str] = None
    cv: Optional[float] = None  # Capital Value
    iv: Optional[float] = None  # Improvements Value
    lv: Optional[float] = None  # Land Value
    owners: Optional[str] = None
    mortgagee: Optional[str] = None
    linked: Optional[bool] = None
    trading_group_id: Optional[int] = None
    last_sale_date: Optional[date] = None
    last_gross_sales_price: Optional[float] = None
    last_sale_source: Optional[str] = None
    last_sale_id: Optional[int] = None

class AddressCreateSchema(BaseModel):
    """Schema for creating addresses"""
    full_address: str
    lat: Optional[float] = None
    lng: Optional[float] = None
    land_area: Optional[float] = None
    land_zone: Optional[str] = None
    land_use: Optional[str] = None

class AddressUpdateSchema(BaseModel):
    """Schema for updating addresses"""
    full_address: Optional[str] = None
    lat: Optional[float] = None
    lng: Optional[float] = None
    land_area: Optional[float] = None
    land_zone: Optional[str] = None
    land_use: Optional[str] = None

class AddressListSchema(PaginatedResponseSchema):
    """Schema for address list responses"""
    results: List[AddressSchema]

# Sale schemas
class SaleSchema(BaseResponseSchema):
    """Sale schema for API responses"""
    sale_id: int
    address: Optional[AddressSchema] = None
    address_id: Optional[int] = None
    sale_date: Optional[datetime] = None
    gross_sales_price: Optional[float] = None
    improvements_value: Optional[float] = None
    total_ha: Optional[float] = None
    vendor: Optional[str] = None
    purchaser: Optional[str] = None
    source: Optional[str] = None
    status: Optional[str] = None
    best_use: Optional[str] = None

class SaleCreateSchema(BaseModel):
    """Schema for creating sales"""
    address_id: int
    sale_date: Optional[datetime] = None
    gross_sales_price: Optional[float] = None
    improvements_value: Optional[float] = None
    total_ha: Optional[float] = None
    vendor: Optional[str] = None
    purchaser: Optional[str] = None
    source: Optional[str] = "FRONTLINE"

class SaleUpdateSchema(BaseModel):
    """Schema for updating sales"""
    sale_date: Optional[datetime] = None
    gross_sales_price: Optional[float] = None
    improvements_value: Optional[float] = None
    total_ha: Optional[float] = None
    vendor: Optional[str] = None
    purchaser: Optional[str] = None
    source: Optional[str] = None

class SaleListSchema(PaginatedResponseSchema):
    """Schema for sale list responses"""
    results: List[SaleSchema]

# Valuation schemas
class ValuationApproach(str, Enum):
    MARKET = "MARKET"

class PropertyTier(int, Enum):
    TIER_1 = 1
    TIER_2 = 2
    TIER_3 = 3

class ValuationSchema(BaseResponseSchema):
    """Valuation schema for API responses"""
    valuation_reference: Optional[str] = None
    valuation_name: Optional[str] = None
    address: Optional[AddressSchema] = None
    address_id: Optional[int] = None
    valuation_date: Optional[date] = None
    total_value: Optional[Decimal] = None
    approach: Optional[ValuationApproach] = ValuationApproach.MARKET
    tier: Optional[PropertyTier] = None
    ccr: Optional[int] = None
    lvr: Optional[int] = None
    creator_id: Optional[int] = None
    assigned_id: Optional[int] = None
    approver_id: Optional[int] = None

class ValuationCreateSchema(BaseModel):
    """Schema for creating valuations"""
    valuation_reference: Optional[str] = None
    valuation_name: Optional[str] = None
    address_id: int
    valuation_date: Optional[date] = None
    total_value: Optional[Decimal] = None
    approach: Optional[ValuationApproach] = ValuationApproach.MARKET
    tier: Optional[PropertyTier] = None

class ValuationUpdateSchema(BaseModel):
    """Schema for updating valuations"""
    valuation_reference: Optional[str] = None
    valuation_name: Optional[str] = None
    valuation_date: Optional[date] = None
    total_value: Optional[Decimal] = None
    approach: Optional[ValuationApproach] = None
    tier: Optional[PropertyTier] = None

class ValuationListSchema(PaginatedResponseSchema):
    """Schema for valuation list responses"""
    results: List[ValuationSchema]

# Customer schemas
class CustomerSchema(BaseResponseSchema):
    """Customer schema for API responses"""
    customer_sk: int
    customer_number: Optional[str] = None
    full_name: Optional[str] = None
    customer_type: Optional[str] = None
    customer_set_code: Optional[str] = None
    segment: Optional[str] = None

class CustomerCreateSchema(BaseModel):
    """Schema for creating customers"""
    customer_number: Optional[str] = None
    full_name: Optional[str] = None
    customer_type: Optional[str] = None
    customer_set_code: Optional[str] = None

class CustomerListSchema(PaginatedResponseSchema):
    """Schema for customer list responses"""
    results: List[CustomerSchema]

# Search and filter schemas
class AddressSearchSchema(BaseModel):
    """Schema for address search parameters"""
    q: Optional[str] = Field(None, description="Search query")
    lat: Optional[float] = Field(None, description="Latitude for location search")
    lng: Optional[float] = Field(None, description="Longitude for location search")
    radius: Optional[float] = Field(None, description="Search radius in kilometers")
    land_use: Optional[str] = Field(None, description="Filter by land use")
    land_zone: Optional[str] = Field(None, description="Filter by land zone")

class SaleSearchSchema(BaseModel):
    """Schema for sale search parameters"""
    address_id: Optional[int] = Field(None, description="Filter by address ID")
    sale_date_from: Optional[date] = Field(None, description="Filter sales from this date")
    sale_date_to: Optional[date] = Field(None, description="Filter sales to this date")
    min_price: Optional[float] = Field(None, description="Minimum sale price")
    max_price: Optional[float] = Field(None, description="Maximum sale price")
    source: Optional[str] = Field(None, description="Filter by sale source")

class ValuationSearchSchema(BaseModel):
    """Schema for valuation search parameters"""
    address_id: Optional[int] = Field(None, description="Filter by address ID")
    valuation_date_from: Optional[date] = Field(None, description="Filter valuations from this date")
    valuation_date_to: Optional[date] = Field(None, description="Filter valuations to this date")
    min_value: Optional[Decimal] = Field(None, description="Minimum valuation value")
    max_value: Optional[Decimal] = Field(None, description="Maximum valuation value")
    approach: Optional[ValuationApproach] = Field(None, description="Filter by valuation approach")

class CustomerSearchSchema(BaseModel):
    """Schema for customer search parameters"""
    match: Optional[str] = Field(None, description="Search customer name or number")
    customer_set_code: Optional[str] = Field(None, description="Filter by customer set code")
    segment: Optional[str] = Field(None, description="Filter by customer segment")
    customer_type: Optional[str] = Field(None, description="Filter by customer type")

# Error schemas
class ErrorSchema(BaseModel):
    """Schema for error responses"""
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None

class ValidationErrorSchema(BaseModel):
    """Schema for validation error responses"""
    error: str = "validation_error"
    message: str
    field_errors: Optional[Dict[str, List[str]]] = None
