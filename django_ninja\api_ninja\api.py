from ninja import Router
from .schemas import HealthSchema, UserSchema, UserPermissionsSchema
from .auth import get_current_user, get_ldap_user_info, get_user_permissions_info, log_api_access

api = Router()

@api.get("/health", response=HealthSchema)
def health_check(request):
    """Health check endpoint"""
    log_api_access(request, "/health")
    return {"status": "ok", "message": "API is running"}

# Import and add other routers
try:
    from .routers.address import router as address_router
    from .routers.sale import router as sale_router
    from .routers.valuation import router as valuation_router
    from .routers.customer import router as customer_router

    api.add_router("/addresses", address_router)
    api.add_router("/sales", sale_router)
    api.add_router("/valuations", valuation_router)
    api.add_router("/customers", customer_router)
except ImportError as e:
    # Handle case where routers are not yet created
    print(f"Warning: Could not import API routers: {e}")

# Add module APIs
try:
    from green_ninja.api import api as green_api
    from finance_ninja.api import api as finance_api
    from ccra_ninja.api import api as ccra_api
    from propertyflow_ninja.api import api as propertyflow_api
    from riskradar_ninja.api import api as riskradar_api

    api.add_router("/green", green_api)
    api.add_router("/finance", finance_api)
    api.add_router("/ccra", ccra_api)
    api.add_router("/propertyflow", propertyflow_api)
    api.add_router("/riskradar", riskradar_api)
except ImportError as e:
    print(f"Warning: Could not import module APIs: {e}")

# Add authentication endpoints
@api.get("/auth/user", response=UserSchema)
def get_authenticated_user(request):
    """Get current authenticated user"""
    user = get_current_user(request)
    log_api_access(request, "/auth/user", user)

    if user.is_authenticated:
        user_info = get_ldap_user_info(user)
        return UserSchema(
            id=user.id,
            username=user_info['username'],
            email=user_info['email'],
            first_name=user_info['first_name'],
            last_name=user_info['last_name'],
            is_staff=user_info['is_staff'],
            is_superuser=user_info['is_superuser'],
            is_active=user_info['is_active'],
            date_joined=user_info['date_joined'],
            last_login=user_info['last_login'],
        )
    return {"error": "Not authenticated"}

@api.get("/auth/permissions", response=UserPermissionsSchema)
def get_authenticated_user_permissions(request):
    """Get current user permissions"""
    user = get_current_user(request)
    log_api_access(request, "/auth/permissions", user)

    if user.is_authenticated:
        permissions_info = get_user_permissions_info(user)
        return UserPermissionsSchema(
            permissions=permissions_info['permissions'],
            groups=permissions_info['groups'],
            is_staff=permissions_info['is_staff'],
            is_superuser=permissions_info['is_superuser'],
        )
    return {"error": "Not authenticated"}

