from ninja import Router
from typing import List
from .schemas import HealthSchema

api = Router()

@api.get("/health", response=HealthSchema)
def health_check(request):
    """Health check endpoint"""
    return {"status": "ok", "message": "API is running"}

# Import and add other routers
try:
    from .routers.address import router as address_router
    from .routers.sale import router as sale_router
    from .routers.valuation import router as valuation_router
    from .routers.customer import router as customer_router
    
    api.add_router("/addresses", address_router)
    api.add_router("/sales", sale_router)
    api.add_router("/valuations", valuation_router)
    api.add_router("/customers", customer_router)
except ImportError as e:
    # Handle case where routers are not yet created
    print(f"Warning: Could not import API routers: {e}")

# Add authentication endpoints
@api.get("/auth/user")
def get_current_user(request):
    """Get current authenticated user"""
    if request.user.is_authenticated:
        return {
            "id": request.user.id,
            "username": request.user.username,
            "email": request.user.email,
            "first_name": request.user.first_name,
            "last_name": request.user.last_name,
            "is_staff": request.user.is_staff,
            "is_superuser": request.user.is_superuser,
        }
    return {"error": "Not authenticated"}

@api.get("/auth/permissions")
def get_user_permissions(request):
    """Get current user permissions"""
    if request.user.is_authenticated:
        return {
            "permissions": list(request.user.get_all_permissions()),
            "groups": [group.name for group in request.user.groups.all()],
        }
    return {"error": "Not authenticated"}
