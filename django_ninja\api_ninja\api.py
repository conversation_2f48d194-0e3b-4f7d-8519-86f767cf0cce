from ninja import Router
from .schemas import HealthSchema, UserSchema, UserPermissionsSchema, LoginSchema
from .auth import get_current_user, get_ldap_user_info, get_user_permissions_info, log_api_access

api = Router()

@api.get("/health", response=HealthSchema)
def health_check(request):
    """Health check endpoint"""
    log_api_access(request, "/health")
    return {"status": "ok", "message": "API is running"}

# Import and add other routers
try:
    from .routers.address import router as address_router
    from .routers.sale import router as sale_router
    from .routers.valuation import router as valuation_router
    from .routers.customer import router as customer_router

    api.add_router("/addresses", address_router)
    api.add_router("/sales", sale_router)
    api.add_router("/valuations", valuation_router)
    api.add_router("/customers", customer_router)
except ImportError as e:
    # Handle case where routers are not yet created
    print(f"Warning: Could not import API routers: {e}")

# Add module APIs
try:
    from green_ninja.api import api as green_api
    from finance_ninja.api import api as finance_api
    from ccra_ninja.api import api as ccra_api
    from propertyflow_ninja.api import api as propertyflow_api
    from riskradar_ninja.api import api as riskradar_api

    api.add_router("/green", green_api)
    api.add_router("/finance", finance_api)
    api.add_router("/ccra", ccra_api)
    api.add_router("/propertyflow", propertyflow_api)
    api.add_router("/riskradar", riskradar_api)
except ImportError as e:
    print(f"Warning: Could not import module APIs: {e}")

# Authentication endpoints
@api.post("/auth/login/")
def login_user(request, payload: LoginSchema):
    """Login endpoint compatible with frontend"""
    user = authenticate(request, username=payload.username, password=payload.password)
    if user is not None:
        login(request, user)
        return {"success": True}
    else:
        raise HttpError(401, "Invalid credentials")

@api.post("/auth/logout/")
def logout_user(request):
    """Logout endpoint compatible with frontend"""
    logout(request)
    return {"success": True}

@api.get("/auth/user/")
def get_current_user(request):
    """Get current user info"""
    if request.user.is_authenticated:
        return {
            "id": request.user.id,
            "username": request.user.username,
            "email": request.user.email,
            "is_staff": request.user.is_staff,
            "is_superuser": request.user.is_superuser,
        }
    return None

