from ninja import Router
from typing import List
from django.shortcuts import get_object_or_404
from django.http import Http404

# Import the new models
from ..models import RiskAssessment, Location
from ..schemas import RiskAssessmentSchema, RiskAssessmentListSchema, RiskAssessmentCreateSchema

router = Router()

@router.get("/", response=RiskAssessmentListSchema)
def list_risk_assessments(request, limit: int = 100, offset: int = 0):
    """List risk assessments with pagination"""
    assessments = RiskAssessment.objects.select_related('location').all()[offset:offset + limit]
    count = RiskAssessment.objects.count()

    return {
        "count": count,
        "results": [RiskAssessmentSchema.from_orm(assessment) for assessment in assessments]
    }

@router.post("/", response=RiskAssessmentSchema)
def create_risk_assessment(request, payload: RiskAssessmentCreateSchema):
    """Create a new risk assessment"""
    # Get the location
    location = get_object_or_404(Location, location_id=payload.location_id)

    assessment_data = payload.dict()
    assessment_data['location'] = location
    del assessment_data['location_id']

    assessment = RiskAssessment.objects.create(**assessment_data)
    return RiskAssessmentSchema.from_orm(assessment)

# The update and delete methods have been removed as they are no longer needed
